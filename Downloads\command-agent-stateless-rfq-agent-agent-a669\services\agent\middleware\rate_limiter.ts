/**
 * Rate limiting middleware
 * Converted from Python Starlette to TypeScript Express/Fastify middleware
 */

import { Request, Response, NextFunction } from 'express';
import { Logger } from 'winston';
import { RedisService } from '../core/redis_client';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

/**
 * Rate limit configuration interface
 */
export interface RateLimitConfig {
  windowMs: number;        // Time window in milliseconds
  maxRequests: number;     // Maximum requests per window
  message?: string;        // Custom error message
  statusCode?: number;     // HTTP status code for rate limit exceeded
  skipSuccessfulRequests?: boolean;  // Don't count successful requests
  skipFailedRequests?: boolean;      // Don't count failed requests
  keyGenerator?: (req: Request) => string;  // Custom key generator
}

/**
 * Rate limit info interface
 */
export interface RateLimitInfo {
  limit: number;
  current: number;
  remaining: number;
  resetTime: Date;
}

/**
 * In-memory rate limiter (fallback when Redis is not available)
 */
class InMemoryRateLimiter {
  private requests: Map<string, number[]> = new Map();

  async checkRateLimit(key: string, windowMs: number, maxRequests: number): Promise<RateLimitInfo> {
    const now = Date.now();
    const windowStart = now - windowMs;

    // Get existing requests for this key
    let keyRequests = this.requests.get(key) || [];

    // Remove old requests outside the window
    keyRequests = keyRequests.filter(timestamp => timestamp > windowStart);

    // Update the requests array
    this.requests.set(key, keyRequests);

    const current = keyRequests.length;
    const remaining = Math.max(0, maxRequests - current);
    const resetTime = new Date(now + windowMs);

    // Add current request if under limit
    if (current < maxRequests) {
      keyRequests.push(now);
      this.requests.set(key, keyRequests);
    }

    return {
      limit: maxRequests,
      current: current + (current < maxRequests ? 1 : 0),
      remaining: remaining - (current < maxRequests ? 1 : 0),
      resetTime
    };
  }

  async reset(key: string): Promise<void> {
    this.requests.delete(key);
  }

  // Cleanup old entries periodically
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [key, timestamps] of this.requests.entries()) {
      const validTimestamps = timestamps.filter(timestamp => now - timestamp < maxAge);
      if (validTimestamps.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, validTimestamps);
      }
    }
  }
}

/**
 * Redis-based rate limiter
 */
class RedisRateLimiter {
  constructor(private redisService: RedisService) {}

  async checkRateLimit(key: string, windowMs: number, maxRequests: number): Promise<RateLimitInfo> {
    const now = Date.now();
    const windowStart = now - windowMs;
    const redisKey = `rate_limit:${key}`;

    try {
      const client = await this.redisService.getClient();

      // Use Redis pipeline for atomic operations
      const pipeline = client.pipeline();

      // Remove old entries
      pipeline.zremrangebyscore(redisKey, 0, windowStart);

      // Count current entries
      pipeline.zcard(redisKey);

      // Add current request
      pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

      // Set expiration
      pipeline.expire(redisKey, Math.ceil(windowMs / 1000));

      const results = await pipeline.exec();

      if (!results) {
        throw new Error('Redis pipeline execution failed');
      }

      const current = (results[1][1] as number) || 0;
      const remaining = Math.max(0, maxRequests - current);
      const resetTime = new Date(now + windowMs);

      // If over limit, remove the request we just added
      if (current >= maxRequests) {
        await client.zrem(redisKey, `${now}-${Math.random()}`);
      }

      return {
        limit: maxRequests,
        current: Math.min(current + 1, maxRequests),
        remaining: Math.max(0, remaining - 1),
        resetTime
      };
    } catch (error) {
      logger.error('Redis rate limiter error, falling back to in-memory', { error });
      // Fallback to in-memory limiter
      const inMemoryLimiter = new InMemoryRateLimiter();
      return inMemoryLimiter.checkRateLimit(key, windowMs, maxRequests);
    }
  }

  async reset(key: string): Promise<void> {
    try {
      const redisKey = `rate_limit:${key}`;
      await this.redisService.delete(redisKey);
    } catch (error) {
      logger.error('Failed to reset rate limit in Redis', { key, error });
    }
  }
}

/**
 * Rate limiter middleware class
 */
export class RateLimiterMiddleware {
  private limiter: InMemoryRateLimiter | RedisRateLimiter;
  private config: Required<RateLimitConfig>;

  constructor(config: RateLimitConfig, redisService?: RedisService) {
    this.config = {
      windowMs: config.windowMs,
      maxRequests: config.maxRequests,
      message: config.message || 'Too many requests, please try again later',
      statusCode: config.statusCode || 429,
      skipSuccessfulRequests: config.skipSuccessfulRequests || false,
      skipFailedRequests: config.skipFailedRequests || false,
      keyGenerator: config.keyGenerator || this.defaultKeyGenerator
    };

    // Use Redis limiter if available, otherwise fall back to in-memory
    this.limiter = redisService
      ? new RedisRateLimiter(redisService)
      : new InMemoryRateLimiter();

    // Setup cleanup for in-memory limiter
    if (this.limiter instanceof InMemoryRateLimiter) {
      setInterval(() => {
        this.limiter.cleanup();
      }, 60000); // Cleanup every minute
    }
  }

  /**
   * Default key generator using client IP
   */
  private defaultKeyGenerator(req: Request): string {
    return req.ip || req.connection.remoteAddress || 'unknown';
  }

  /**
   * Express middleware function
   */
  middleware() {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const key = this.config.keyGenerator(req);
        const rateLimitInfo = await this.limiter.checkRateLimit(
          key,
          this.config.windowMs,
          this.config.maxRequests
        );

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': rateLimitInfo.limit.toString(),
          'X-RateLimit-Remaining': rateLimitInfo.remaining.toString(),
          'X-RateLimit-Reset': rateLimitInfo.resetTime.getTime().toString()
        });

        // Check if rate limit exceeded
        if (rateLimitInfo.remaining <= 0) {
          logger.warn('Rate limit exceeded', {
            key,
            limit: rateLimitInfo.limit,
            current: rateLimitInfo.current,
            path: req.path,
            method: req.method
          });

          res.status(this.config.statusCode).json({
            error: this.config.message,
            retryAfter: Math.ceil(this.config.windowMs / 1000)
          });
          return;
        }

        next();
      } catch (error) {
        logger.error('Rate limiter middleware error', { error });
        // On error, allow the request to proceed
        next();
      }
    };
  }

  /**
   * Reset rate limit for a specific key
   */
  async reset(key: string): Promise<void> {
    await this.limiter.reset(key);
  }
}

/**
 * Fastify rate limiter plugin
 */
export const fastifyRateLimiter = {
  name: 'rate-limiter',
  register: async (fastify: any, options: RateLimitConfig & { redisService?: RedisService }) => {
    const rateLimiter = new RateLimiterMiddleware(options, options.redisService);

    fastify.addHook('preHandler', async (request: any, reply: any) => {
      try {
        const key = options.keyGenerator ? options.keyGenerator(request) : request.ip;
        const rateLimitInfo = await rateLimiter['limiter'].checkRateLimit(
          key,
          options.windowMs,
          options.maxRequests
        );

        // Set rate limit headers
        reply.headers({
          'X-RateLimit-Limit': rateLimitInfo.limit.toString(),
          'X-RateLimit-Remaining': rateLimitInfo.remaining.toString(),
          'X-RateLimit-Reset': rateLimitInfo.resetTime.getTime().toString()
        });

        // Check if rate limit exceeded
        if (rateLimitInfo.remaining <= 0) {
          logger.warn('Rate limit exceeded', {
            key,
            limit: rateLimitInfo.limit,
            current: rateLimitInfo.current,
            path: request.url,
            method: request.method
          });

          reply.status(options.statusCode || 429).send({
            error: options.message || 'Too many requests, please try again later',
            retryAfter: Math.ceil(options.windowMs / 1000)
          });
          return;
        }
      } catch (error) {
        logger.error('Rate limiter hook error', { error });
        // On error, allow the request to proceed
      }
    });
  }
};

/**
 * Create rate limiter middleware with default configuration
 */
export function createRateLimiter(
  callsPerMinute: number = 60,
  redisService?: RedisService
): RateLimiterMiddleware {
  return new RateLimiterMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: callsPerMinute
  }, redisService);
}

/**
 * Create API rate limiter (stricter limits)
 */
export function createApiRateLimiter(redisService?: RedisService): RateLimiterMiddleware {
  return new RateLimiterMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    message: 'API rate limit exceeded. Please try again later.'
  }, redisService);
}

/**
 * Create memory service rate limiter (more restrictive)
 */
export function createMemoryRateLimiter(redisService?: RedisService): RateLimiterMiddleware {
  return new RateLimiterMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    message: 'Memory service rate limit exceeded. Please try again later.'
  }, redisService);
}

/**
 * Create user-specific rate limiter
 */
export function createUserRateLimiter(redisService?: RedisService): RateLimiterMiddleware {
  return new RateLimiterMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200,
    keyGenerator: (req: Request) => {
      // Use user ID from auth token if available, otherwise fall back to IP
      const userId = (req as any).user?.id;
      return userId || req.ip || req.connection.remoteAddress || 'unknown';
    },
    message: 'User rate limit exceeded. Please try again later.'
  }, redisService);
}

/**
 * Rate limiter utilities
 */
export class RateLimiterUtils {
  /**
   * Get client identifier from request
   */
  static getClientId(req: Request): string {
    // Try to get user ID first
    const userId = (req as any).user?.id;
    if (userId) return `user:${userId}`;

    // Fall back to IP address
    const ip = req.ip || req.connection.remoteAddress;
    if (ip) return `ip:${ip}`;

    // Last resort
    return 'unknown';
  }

  /**
   * Get rate limit key for specific endpoint
   */
  static getEndpointKey(req: Request, endpoint: string): string {
    const clientId = this.getClientId(req);
    return `${endpoint}:${clientId}`;
  }

  /**
   * Create custom key generator for specific endpoints
   */
  static createEndpointKeyGenerator(endpoint: string) {
    return (req: Request): string => {
      return this.getEndpointKey(req, endpoint);
    };
  }

  /**
   * Parse rate limit headers from response
   */
  static parseRateLimitHeaders(headers: Record<string, string>): RateLimitInfo | null {
    const limit = parseInt(headers['x-ratelimit-limit']);
    const remaining = parseInt(headers['x-ratelimit-remaining']);
    const reset = parseInt(headers['x-ratelimit-reset']);

    if (isNaN(limit) || isNaN(remaining) || isNaN(reset)) {
      return null;
    }

    return {
      limit,
      current: limit - remaining,
      remaining,
      resetTime: new Date(reset)
    };
  }
}

// Export default rate limiter
export default RateLimiterMiddleware;