/**
 * Middleware module exports
 * Centralized exports for all middleware components
 */

// Error handling middleware
export {
  ErrorHandlerMiddleware,
  AppError,
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  ValidationError,
  InternalServerError,
  GlobalErrorHandler,
  fastifyErrorHandler,
  type ErrorResponse
} from './error_handler';

// Rate limiting middleware
export {
  RateLimiterMiddleware,
  RateLimiterUtils,
  createRateLimiter,
  createApiRateLimiter,
  createMemoryRateLimiter,
  createUserRateLimiter,
  fastifyRateLimiter,
  type RateLimitConfig,
  type RateLimitInfo
} from './rate_limiter';

// Default exports
export { default as <PERSON>rrorHandler } from './error_handler';
export { default as RateLimiter } from './rate_limiter';