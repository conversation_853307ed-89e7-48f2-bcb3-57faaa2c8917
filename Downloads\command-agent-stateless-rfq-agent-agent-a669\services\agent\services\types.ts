/**
 * TypeScript type definitions for AI Agent Services
 * Comprehensive interfaces and types for all data structures
 */

// ========== COMMON TYPES ==========

export interface BaseMetadata {
  [key: string]: any;
}

export interface TimestampedEntity {
  created_at?: string;
  updated_at?: string;
  timestamp?: string;
}

// ========== EMBEDDING SERVICE TYPES ==========

export interface EmbeddingVector extends Array<number> {}

export interface EmbeddingItem {
  id?: string;
  content: string;
  embedding: EmbeddingVector;
  similarity?: number;
  metadata?: BaseMetadata;
}

export interface BatchEmbeddingRequest {
  texts: string[];
  dimension?: number;
}

export interface SimilaritySearchRequest {
  query_embedding: EmbeddingVector;
  candidate_embeddings: EmbeddingItem[];
  threshold?: number;
}

// ========== ERROR MANAGER TYPES ==========

export interface ErrorInfo {
  error_type: string;
  step: string;
  message: string;
  signature: string;
  timestamp: string;
  execution_id: string;
  context?: BaseMetadata;
  suggested_solutions?: string[];
  solution_confidence?: number;
}

export interface ErrorPattern {
  id?: string;
  error_signature: string;
  error_type: string;
  description: string;
  embedding: EmbeddingVector;
  occurrence_count: number;
  resolved_count: number;
  solution_steps: string[];
  context_pattern: BaseMetadata;
  step_name: string;
}

export interface ErrorAnalytics {
  error_types: Array<{
    type: string;
    count: number;
    total_occurrences: number;
    total_resolutions: number;
    avg_resolution_rate: number;
  }>;
  recent_trends: Array<{
    error_date: string;
    pattern_count: number;
    occurrence_count: number;
  }>;
  top_unresolved: Array<{
    error_type: string;
    description: string;
    occurrence_count: number;
    resolved_count: number;
    unresolved_count: number;
  }>;
  generated_at: string;
}

// ========== MEMORY SERVICE TYPES ==========

export interface MemoryItem extends TimestampedEntity {
  id: string;
  content: string;
  memory_type: string;
  metadata: BaseMetadata;
  importance_score: number;
  embedding?: EmbeddingVector;
  similarity?: number;
  last_accessed?: string;
}

export interface ConversationData {
  messages: Array<{
    role: string;
    content: string;
    timestamp?: string;
  }>;
  metadata?: BaseMetadata;
}

export interface ConversationMemory extends TimestampedEntity {
  id: string;
  user_id: string;
  conversation_data: ConversationData;
  summary: string;
  message_count: number;
}

export interface MemorySearchRequest {
  query: string;
  limit?: number;
  memory_types?: string[];
  importance_threshold?: number;
  similarity_threshold?: number;
}

export interface MemoryStatistics {
  total_memories: number;
  recent_memories_24h: number;
  memory_clusters: number;
  memory_types: Array<{
    type: string;
    count: number;
    avg_importance: number;
  }>;
  timestamp: string;
  error?: string;
}

// ========== MONITORING SERVICE TYPES ==========

export interface MetricLabels {
  [key: string]: string;
}

export interface SystemHealth {
  timestamp: string;
  service_status: string;
  metrics_collected: boolean;
  prometheus_endpoint: string;
  grafana_dashboard: string;
  alert_rules_active: boolean;
}

export interface MetricsSummary {
  [key: string]: string;
}

export interface MonitoringCapabilities {
  service_id: string;
  monitoring_type: string;
  capabilities: string[];
  metrics_categories: string[];
  factor_coverage: string;
  description: string;
  version: string;
  endpoints: {
    [key: string]: string;
  };
}

// ========== PERPLEXITY SERVICE TYPES ==========

export interface PerplexityMessage {
  role: string;
  content: string;
}

export interface PerplexityTool {
  type: string;
  function: {
    name: string;
    description: string;
    parameters: {
      type: string;
      properties: BaseMetadata;
      required: string[];
    };
  };
}

export interface PerplexityResponse {
  choices?: Array<{
    message: {
      content: string;
      tool_calls?: Array<{
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
  }>;
}

export interface MarketIntelligence {
  price_range: string;
  market_trends: string;
  supplier_recommendations: string[];
  risk_factors: string[];
  compliance_requirements?: string[];
  market_insights?: string;
}

export interface MarketResearchResult {
  content: string;
  structured_data: MarketIntelligence;
  timestamp: string;
  error?: string;
}

export interface VendorInfo {
  name: string;
  specialties: string[];
  location: string;
  reputation: string;
  contact_info: string;
}

export interface VendorDiscoveryResult {
  content: string;
  vendors: VendorInfo[];
  insights: string;
  timestamp: string;
  error?: string;
}

// ========== STATELESS REDUCER TYPES ==========

export enum ActionType {
  INITIALIZE = "initialize",
  UPDATE_CONTEXT = "update_context",
  ADD_MEMORY = "add_memory",
  SET_STATUS = "set_status",
  UPDATE_PROGRESS = "update_progress",
  ADD_RESULT = "add_result",
  SET_ERROR = "set_error",
  CLEAR_ERROR = "clear_error",
  MERGE_STATE = "merge_state"
}

export interface Action {
  type: ActionType;
  payload: BaseMetadata;
  timestamp: Date;
  source: string;
  metadata?: BaseMetadata;
}

export interface AgentState {
  execution_id: string;
  status: string;
  progress: number;
  context: BaseMetadata;
  memories: Array<{
    content: string;
    type: string;
    timestamp: string;
    metadata: BaseMetadata;
  }>;
  results: BaseMetadata;
  errors: string[];
  metadata: BaseMetadata;
  last_updated: Date;
}

export interface WorkflowStepResult {
  success: boolean;
  data?: any;
  error?: string;
}

export type WorkflowStepFunction = (input: BaseMetadata) => Promise<WorkflowStepResult>;

export interface ServiceCapabilities {
  service_id: string;
  service_type: string;
  capabilities: string[];
  description: string;
  version: string;
  factor_compliance?: string[];
  guarantees?: string[];
}

// ========== TOOL SERVICE TYPES ==========

export interface ToolDefinition extends TimestampedEntity {
  id?: string;
  name: string;
  description: string;
  schema: {
    type: string;
    properties: BaseMetadata;
    required: string[];
  };
  implementation: string;
  category: string;
  version: string;
  is_active: boolean;
}

export interface ToolCall extends TimestampedEntity {
  tool_name: string;
  input_data: BaseMetadata;
  output_data: BaseMetadata;
  execution_time: number;
  timestamp: string;
  success: boolean;
  error_info?: string;
}

export interface ToolStatistics {
  tool_name?: string;
  total_calls: number;
  successful_calls: number;
  failed_calls: number;
  avg_execution_time: number;
  max_execution_time?: number;
  first_call?: string;
  last_call?: string;
  success_rate: number;
}

export interface ToolExecutionContext {
  execution_id?: string;
  user_id?: string;
  metadata?: BaseMetadata;
}

// ========== UNIVERSAL INPUT HANDLER TYPES ==========

export enum InputSource {
  EMAIL = "email",
  API = "api",
  WEBHOOK = "webhook",
  FILE_UPLOAD = "file_upload",
  CHAT = "chat",
  VOICE = "voice",
  SMS = "sms",
  SLACK = "slack",
  TEAMS = "teams",
  WEB_FORM = "web_form",
  UNKNOWN = "unknown"
}

export enum InputFormat {
  TEXT = "text",
  JSON = "json",
  XML = "xml",
  PDF = "pdf",
  EMAIL_MSG = "email_msg",
  VOICE_AUDIO = "voice_audio",
  CSV = "csv",
  EXCEL = "excel",
  UNKNOWN = "unknown"
}

export interface InputContext {
  source: InputSource;
  format: InputFormat;
  timestamp: Date;
  sender_info?: BaseMetadata;
  metadata?: BaseMetadata;
  priority: string;
  requires_response: boolean;
}

export interface ParsedRequest {
  intent: string;
  category: string;
  region: string;
  budget_range?: string;
  urgency: string;
  department: string;
  raw_request: string;
  confidence_score: number;
  extracted_entities: BaseMetadata;
}

export interface InputProcessingResult {
  success: boolean;
  input_processed: boolean;
  source: string;
  format: string;
  intent: string;
  confidence: number;
  workflow_response: BaseMetadata;
  formatted_response: BaseMetadata;
  handler_id: string;
  processing_time: string;
  error?: string;
}

export interface HandlerCapabilities {
  handler_id: string;
  handler_type: string;
  supported_sources: string[];
  supported_formats: string[];
  supported_intents: string[];
  capabilities: string[];
  description: string;
  version: string;
  factor_compliance: string[];
}

// ========== WORKFLOW TYPES ==========

export interface WorkflowMetadata {
  start_time: string;
  end_time: string;
  steps_completed: number;
  memories_created: number;
}

export interface WorkflowResponse {
  success: boolean;
  execution_id: string;
  final_status: string;
  progress: number;
  results: BaseMetadata;
  errors: string[];
  workflow_metadata: WorkflowMetadata;
  state_transitions: Array<{
    step: string;
    timestamp: string;
    metadata: BaseMetadata;
  }>;
  service_info: {
    service_id: string;
    factor_compliance: string[];
    functional_guarantees: string[];
  };
}

// ========== DATABASE TYPES ==========

export interface DatabaseQueryParams {
  [key: string]: any;
}

export interface DatabaseResult {
  [key: string]: any;
}

// ========== HTTP CLIENT TYPES ==========

export interface HttpClientConfig {
  timeout?: number;
  headers?: Record<string, string>;
  baseURL?: string;
}

export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// ========== CONFIGURATION TYPES ==========

export interface ServiceConfig {
  perplexity_api_key: string;
  perplexity_model: string;
  perplexity_max_tokens: number;
  perplexity_temperature: number;
  database_url?: string;
  redis_url?: string;
  log_level?: string;
}

// ========== LOGGER TYPES ==========

export interface Logger {
  debug(message: string, meta?: BaseMetadata): void;
  info(message: string, meta?: BaseMetadata): void;
  warn(message: string, meta?: BaseMetadata): void;
  error(message: string, meta?: BaseMetadata): void;
}

// ========== UTILITY TYPES ==========

export type AsyncFunction<T = any, R = any> = (input: T) => Promise<R>;
export type SyncFunction<T = any, R = any> = (input: T) => R;
export type AnyFunction<T = any, R = any> = AsyncFunction<T, R> | SyncFunction<T, R>;

export interface KeywordWeights {
  [keyword: string]: number;
}

export interface ValidationResult {
  valid: boolean;
  errors?: string[];
}
