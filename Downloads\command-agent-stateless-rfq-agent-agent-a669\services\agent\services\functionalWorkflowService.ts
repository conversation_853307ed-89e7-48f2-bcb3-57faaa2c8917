/**
 * Functional Workflow Service implementing stateless reducer patterns - TypeScript version
 * Implements Factor 8: Own Your Control Flow with LangGraph-style workflow orchestration
 */

import {
  WorkflowState,
  WorkflowStep,
  WorkflowStepFunction,
  WorkflowDefinition,
  WorkflowExecution,
  WorkflowCapabilities,
  BaseMetadata,
  Logger
} from './types';
import { StatelessAgentService } from './statelessReducer';
import { MemoryService } from './memoryService';
import { PerplexityService } from './perplexityService';
import { ToolService } from './toolService';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

/**
 * Functional workflow orchestration service
 * Implements stateless, composable workflow patterns with LangGraph-style control flow
 */
export class FunctionalWorkflowService {
  private readonly statelessService: StatelessAgentService;
  private readonly memoryService: MemoryService;
  private readonly perplexityService: PerplexityService;
  private readonly toolService: ToolService;
  private readonly registeredWorkflows: Map<string, WorkflowDefinition>;

  constructor() {
    this.statelessService = new StatelessAgentService();
    this.memoryService = new MemoryService();
    this.perplexityService = new PerplexityService();
    this.toolService = new ToolService();
    this.registeredWorkflows = new Map();
  }

  /**
   * Register a workflow definition
   */
  registerWorkflow(
    name: string,
    description: string,
    steps: WorkflowStep[],
    metadata: BaseMetadata = {}
  ): void {
    const workflow: WorkflowDefinition = {
      name,
      description,
      steps,
      metadata,
      version: "1.0.0",
      created_at: new Date().toISOString()
    };

    this.registeredWorkflows.set(name, workflow);
    logger.info("Workflow registered", { name, steps_count: steps.length });
  }

  /**
   * Execute a workflow using functional composition patterns
   * Implements Factor 8: Control Flow Management
   */
  async executeWorkflow(
    workflowName: string,
    initialInput: BaseMetadata,
    executionId: string,
    userId: string = "system"
  ): Promise<WorkflowExecution> {
    const startTime = new Date();
    logger.info("Starting workflow execution", { workflow: workflowName, execution_id: executionId });

    try {
      // Get workflow definition
      const workflow = this.registeredWorkflows.get(workflowName);
      if (!workflow) {
        throw new Error(`Workflow '${workflowName}' not found`);
      }

      // Initialize workflow state
      let currentState = this.statelessService.createInitialState(executionId, {
        workflow_name: workflowName,
        user_id: userId,
        initial_input: initialInput
      });

      // Set initial status
      currentState = this.statelessService.setStatus(currentState, "running");

      // Execute workflow steps sequentially
      const stepResults: BaseMetadata = {};
      let currentInput = initialInput;

      for (let i = 0; i < workflow.steps.length; i++) {
        const step = workflow.steps[i];
        const stepName = step.name;

        logger.info("Executing workflow step", { 
          workflow: workflowName, 
          step: stepName, 
          step_index: i + 1 
        });

        try {
          // Update state for current step
          currentState = this.statelessService.setStatus(currentState, `running_${stepName}`);
          currentState = this.statelessService.updateProgress(
            currentState, 
            (i / workflow.steps.length)
          );

          // Execute step function
          const stepResult = await this.executeWorkflowStep(
            step,
            currentInput,
            currentState,
            executionId
          );

          // Store step result
          stepResults[stepName] = stepResult;
          currentState = this.statelessService.addResult(currentState, stepName, stepResult);

          // Add memory of step completion
          currentState = this.statelessService.addMemory(
            currentState,
            `Completed step: ${stepName}`,
            "workflow_step",
            { step_result: stepResult }
          );

          // Prepare input for next step
          currentInput = {
            ...currentInput,
            previous_step_result: stepResult,
            step_results: stepResults
          };

          logger.info("Workflow step completed", { 
            workflow: workflowName, 
            step: stepName,
            result_type: typeof stepResult
          });

        } catch (stepError) {
          logger.error("Workflow step failed", {
            workflow: workflowName,
            step: stepName,
            error: String(stepError)
          });

          // Handle step failure
          currentState = this.statelessService.setError(
            currentState,
            `Step ${stepName} failed: ${String(stepError)}`
          );

          // Check if step is optional
          if (!step.optional) {
            throw stepError;
          }

          // Continue with null result for optional step
          stepResults[stepName] = null;
          currentState = this.statelessService.addResult(currentState, stepName, null);
        }
      }

      // Mark workflow as completed
      currentState = this.statelessService.setStatus(currentState, "completed");
      currentState = this.statelessService.updateProgress(currentState, 1.0);

      // Store workflow execution memory
      await this.memoryService.storeWorkflowMemory(
        {
          workflow_name: workflowName,
          execution_id: executionId,
          step_results: stepResults,
          success: true,
          duration: (new Date().getTime() - startTime.getTime()) / 1000
        },
        executionId,
        userId
      );

      const execution: WorkflowExecution = {
        execution_id: executionId,
        workflow_name: workflowName,
        status: "completed",
        start_time: startTime.toISOString(),
        end_time: new Date().toISOString(),
        duration: (new Date().getTime() - startTime.getTime()) / 1000,
        step_results: stepResults,
        final_state: currentState,
        success: true
      };

      logger.info("Workflow execution completed", {
        workflow: workflowName,
        execution_id: executionId,
        duration: execution.duration
      });

      return execution;

    } catch (error) {
      logger.error("Workflow execution failed", {
        workflow: workflowName,
        execution_id: executionId,
        error: String(error)
      });

      const execution: WorkflowExecution = {
        execution_id: executionId,
        workflow_name: workflowName,
        status: "failed",
        start_time: startTime.toISOString(),
        end_time: new Date().toISOString(),
        duration: (new Date().getTime() - startTime.getTime()) / 1000,
        step_results: {},
        final_state: this.statelessService.createInitialState(executionId),
        success: false,
        error: String(error)
      };

      return execution;
    }
  }

  /**
   * Execute a single workflow step
   */
  private async executeWorkflowStep(
    step: WorkflowStep,
    input: BaseMetadata,
    currentState: WorkflowState,
    executionId: string
  ): Promise<any> {
    const stepStartTime = new Date();

    try {
      // Check step conditions if any
      if (step.condition && !await this.evaluateStepCondition(step.condition, input, currentState)) {
        logger.info("Step condition not met, skipping", { step: step.name });
        return { skipped: true, reason: "condition_not_met" };
      }

      // Execute step based on type
      let result: any;

      switch (step.type) {
        case "tool_call":
          result = await this.executeToolCallStep(step, input);
          break;
        case "memory_search":
          result = await this.executeMemorySearchStep(step, input);
          break;
        case "ai_research":
          result = await this.executeAiResearchStep(step, input);
          break;
        case "data_processing":
          result = await this.executeDataProcessingStep(step, input);
          break;
        case "conditional":
          result = await this.executeConditionalStep(step, input, currentState);
          break;
        case "parallel":
          result = await this.executeParallelStep(step, input, currentState, executionId);
          break;
        default:
          throw new Error(`Unknown step type: ${step.type}`);
      }

      const duration = (new Date().getTime() - stepStartTime.getTime()) / 1000;
      logger.debug("Step executed successfully", {
        step: step.name,
        type: step.type,
        duration
      });

      return result;

    } catch (error) {
      const duration = (new Date().getTime() - stepStartTime.getTime()) / 1000;
      logger.error("Step execution failed", {
        step: step.name,
        type: step.type,
        duration,
        error: String(error)
      });
      throw error;
    }
  }

  /**
   * Execute tool call step
   */
  private async executeToolCallStep(step: WorkflowStep, input: BaseMetadata): Promise<any> {
    const toolName = step.parameters?.tool_name;
    if (!toolName) {
      throw new Error("Tool name not specified for tool_call step");
    }

    const toolInput = step.parameters?.tool_input || input;
    return await this.toolService.executeTool(toolName, toolInput);
  }

  /**
   * Execute memory search step
   */
  private async executeMemorySearchStep(step: WorkflowStep, input: BaseMetadata): Promise<any> {
    const query = step.parameters?.query || input.query || "";
    const memoryTypes = step.parameters?.memory_types;
    const limit = step.parameters?.limit || 10;

    return await this.memoryService.retrieveContext(query, limit, memoryTypes);
  }

  /**
   * Execute AI research step
   */
  private async executeAiResearchStep(step: WorkflowStep, input: BaseMetadata): Promise<any> {
    const query = step.parameters?.query || input.query || "";
    const researchType = step.parameters?.research_type || "market";

    if (researchType === "market") {
      return await this.perplexityService.researchMarket(query);
    } else if (researchType === "vendor") {
      return await this.perplexityService.discoverVendors(query);
    } else {
      throw new Error(`Unknown research type: ${researchType}`);
    }
  }

  /**
   * Execute data processing step
   */
  private async executeDataProcessingStep(step: WorkflowStep, input: BaseMetadata): Promise<any> {
    const processingType = step.parameters?.processing_type;
    const data = step.parameters?.data || input;

    switch (processingType) {
      case "filter":
        return this.filterData(data, step.parameters?.filter_criteria || {});
      case "transform":
        return this.transformData(data, step.parameters?.transformation || {});
      case "aggregate":
        return this.aggregateData(data, step.parameters?.aggregation || {});
      default:
        throw new Error(`Unknown processing type: ${processingType}`);
    }
  }

  /**
   * Execute conditional step
   */
  private async executeConditionalStep(
    step: WorkflowStep,
    input: BaseMetadata,
    currentState: WorkflowState
  ): Promise<any> {
    const condition = step.parameters?.condition;
    if (!condition) {
      throw new Error("Condition not specified for conditional step");
    }

    const conditionMet = await this.evaluateStepCondition(condition, input, currentState);
    
    if (conditionMet) {
      const trueSteps = step.parameters?.true_steps || [];
      return await this.executeSubSteps(trueSteps, input, currentState);
    } else {
      const falseSteps = step.parameters?.false_steps || [];
      return await this.executeSubSteps(falseSteps, input, currentState);
    }
  }

  /**
   * Execute parallel step
   */
  private async executeParallelStep(
    step: WorkflowStep,
    input: BaseMetadata,
    currentState: WorkflowState,
    executionId: string
  ): Promise<any> {
    const parallelSteps = step.parameters?.parallel_steps || [];
    
    const promises = parallelSteps.map(async (subStep: WorkflowStep) => {
      return await this.executeWorkflowStep(subStep, input, currentState, executionId);
    });

    const results = await Promise.allSettled(promises);
    
    return results.map((result, index) => ({
      step_name: parallelSteps[index].name,
      status: result.status,
      result: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? String(result.reason) : null
    }));
  }

  /**
   * Execute sub-steps for conditional workflows
   */
  private async executeSubSteps(
    subSteps: WorkflowStep[],
    input: BaseMetadata,
    currentState: WorkflowState
  ): Promise<any> {
    const results: BaseMetadata = {};
    let currentInput = input;

    for (const subStep of subSteps) {
      const result = await this.executeWorkflowStep(subStep, currentInput, currentState, "sub");
      results[subStep.name] = result;
      currentInput = { ...currentInput, previous_result: result };
    }

    return results;
  }

  /**
   * Evaluate step condition
   */
  private async evaluateStepCondition(
    condition: BaseMetadata,
    input: BaseMetadata,
    currentState: WorkflowState
  ): Promise<boolean> {
    // Simple condition evaluation - can be enhanced
    const conditionType = condition.type;

    switch (conditionType) {
      case "field_exists":
        return condition.field in input;
      case "field_equals":
        return input[condition.field] === condition.value;
      case "field_contains":
        const fieldValue = String(input[condition.field] || "");
        return fieldValue.includes(String(condition.value));
      case "state_status":
        return currentState.status === condition.status;
      default:
        return true; // Default to true for unknown conditions
    }
  }

  // ========== DATA PROCESSING UTILITIES ==========

  private filterData(data: any, criteria: BaseMetadata): any {
    if (Array.isArray(data)) {
      return data.filter(item => {
        for (const [key, value] of Object.entries(criteria)) {
          if (item[key] !== value) {
            return false;
          }
        }
        return true;
      });
    }
    return data;
  }

  private transformData(data: any, transformation: BaseMetadata): any {
    if (Array.isArray(data)) {
      return data.map(item => {
        const transformed = { ...item };
        for (const [key, transform] of Object.entries(transformation)) {
          if (typeof transform === 'string' && transform.startsWith('${') && transform.endsWith('}')) {
            const fieldName = transform.slice(2, -1);
            transformed[key] = item[fieldName];
          } else {
            transformed[key] = transform;
          }
        }
        return transformed;
      });
    }
    return data;
  }

  private aggregateData(data: any, aggregation: BaseMetadata): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const result: BaseMetadata = {};
    const aggType = aggregation.type || "count";

    switch (aggType) {
      case "count":
        result.count = data.length;
        break;
      case "sum":
        const field = aggregation.field;
        result.sum = data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
        break;
      case "average":
        const avgField = aggregation.field;
        const values = data.map(item => Number(item[avgField]) || 0);
        result.average = values.reduce((sum, val) => sum + val, 0) / values.length;
        break;
      default:
        result.count = data.length;
    }

    return result;
  }

  /**
   * Get registered workflows
   */
  getRegisteredWorkflows(): string[] {
    return Array.from(this.registeredWorkflows.keys());
  }

  /**
   * Get workflow definition
   */
  getWorkflowDefinition(name: string): WorkflowDefinition | null {
    return this.registeredWorkflows.get(name) || null;
  }

  /**
   * Get service capabilities
   */
  getWorkflowCapabilities(): WorkflowCapabilities {
    return {
      service_id: "functional_workflow_service",
      workflow_types: [
        "sequential",
        "conditional",
        "parallel",
        "mixed"
      ],
      step_types: [
        "tool_call",
        "memory_search", 
        "ai_research",
        "data_processing",
        "conditional",
        "parallel"
      ],
      features: [
        "stateless_execution",
        "functional_composition",
        "error_recovery",
        "step_conditions",
        "parallel_execution",
        "memory_integration"
      ],
      version: "1.0.0",
      registered_workflows: this.getRegisteredWorkflows().length
    };
  }
}
