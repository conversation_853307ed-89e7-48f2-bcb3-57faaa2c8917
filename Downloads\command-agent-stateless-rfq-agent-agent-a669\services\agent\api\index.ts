/**
 * API module exports
 * Centralized exports for all API components
 */

// Input Gateway
export {
  InputGatewayRoutes,
  createInputGatewayRoutes,
  inputGatewayPlugin,
  MockUniversalInputHandler,
  InputSource,
  InputFormat,
  createInputContext,
  validateInputRequest,
  formatErrorResponse,
  type TextInputRequest,
  type JsonInputRequest,
  type EmailInputRequest,
  type ChatInputRequest,
  type InputContext,
  type ProcessingResult,
  type UniversalInputHandler
} from './input_gateway';

// Default export
export { default as InputGateway } from './input_gateway';