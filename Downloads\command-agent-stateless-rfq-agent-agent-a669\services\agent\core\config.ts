/**
 * Core configuration module implementing 12-Factor App principles
 * Centralized settings management with environment-based configuration
 * Converted from Python Pydantic settings to TypeScript configuration
 */

import { IsString, IsNumber, IsBoolean, IsArray, IsOptional, validateOrReject } from 'class-validator';
import { Transform } from 'class-transformer';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Application settings following 12-Factor App principles
 */
export class Settings {
  // ========== APPLICATION SETTINGS ==========
  @IsString()
  app_name: string = process.env.APP_NAME || "LangGraph AI Agent";

  @IsString()
  @Transform(({ value }) => this.validateEnvironment(value))
  environment: string = process.env.ENVIRONMENT || "development";

  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  debug: boolean = process.env.DEBUG === 'true' || false;

  @IsString()
  @Transform(({ value }) => this.validateLogLevel(value))
  log_level: string = process.env.LOG_LEVEL || "INFO";

  // ========== DATABASE CONFIGURATION ==========
  @IsString()
  @Transform(({ value }) => this.validateDatabaseUrl(value))
  database_url!: string;

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 20)
  database_pool_size: number = parseInt(process.env.DATABASE_POOL_SIZE || '20');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 30)
  database_max_overflow: number = parseInt(process.env.DATABASE_MAX_OVERFLOW || '30');

  // ========== REDIS CONFIGURATION ==========
  @IsString()
  @Transform(({ value }) => this.validateRedisUrl(value))
  redis_url!: string;

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 100)
  redis_max_connections: number = parseInt(process.env.REDIS_MAX_CONNECTIONS || '100');

  // ========== API KEYS ==========
  @IsString()
  perplexity_api_key!: string;

  // ========== SECURITY ==========
  @IsString()
  secret_key: string = process.env.SECRET_KEY || "development-key-change-in-production";

  @IsString()
  jwt_secret: string = process.env.JWT_SECRET || "jwt-secret-change-in-production";

  @IsString()
  encryption_key: string = process.env.ENCRYPTION_KEY || "encryption-key-change";

  // ========== CORS SETTINGS ==========
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(origin => origin.trim());
    }
    return value || ["http://localhost:3000", "http://localhost:8000"];
  })
  allowed_origins: string[] = process.env.ALLOWED_ORIGINS?.split(',').map(o => o.trim()) ||
    ["http://localhost:3000", "http://localhost:8000"];

  // ========== RATE LIMITING ==========
  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 100)
  rate_limit_requests: number = parseInt(process.env.RATE_LIMIT_REQUESTS || '100');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 60)
  rate_limit_window: number = parseInt(process.env.RATE_LIMIT_WINDOW || '60');

  // ========== AGENT CONFIGURATION ==========
  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 50)
  max_concurrent_workflows: number = parseInt(process.env.MAX_CONCURRENT_WORKFLOWS || '50');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 3600)
  workflow_timeout: number = parseInt(process.env.WORKFLOW_TIMEOUT || '3600'); // 1 hour

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 32000)
  context_window_size: number = parseInt(process.env.CONTEXT_WINDOW_SIZE || '32000');

  // ========== MEMORY SYSTEM ==========
  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 32)
  embedding_batch_size: number = parseInt(process.env.EMBEDDING_BATCH_SIZE || '32');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 1536)
  vector_dimension: number = parseInt(process.env.VECTOR_DIMENSION || '1536');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 3600)
  memory_consolidation_interval: number = parseInt(process.env.MEMORY_CONSOLIDATION_INTERVAL || '3600');

  // ========== PERPLEXITY CONFIGURATION ==========
  @IsString()
  perplexity_model: string = process.env.PERPLEXITY_MODEL || "sonar-pro";

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 4000)
  perplexity_max_tokens: number = parseInt(process.env.PERPLEXITY_MAX_TOKENS || '4000');

  @IsNumber()
  @Transform(({ value }) => parseFloat(value) || 0.1)
  perplexity_temperature: number = parseFloat(process.env.PERPLEXITY_TEMPERATURE || '0.1');

  // ========== MONITORING ==========
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_monitoring: boolean = process.env.ENABLE_MONITORING === 'true' || true;

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 8001)
  metrics_port: number = parseInt(process.env.METRICS_PORT || '8001');

  // ========== FEATURE FLAGS ==========
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_human_in_loop: boolean = process.env.ENABLE_HUMAN_IN_LOOP === 'true' || true;

  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_advanced_memory: boolean = process.env.ENABLE_ADVANCED_MEMORY === 'true' || true;

  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_async_processing: boolean = process.env.ENABLE_ASYNC_PROCESSING === 'true' || true;

  // ========== CELERY CONFIGURATION ==========
  @IsString()
  celery_broker_url: string = process.env.RABBITMQ_URL || process.env.REDIS_URL || "redis://localhost:6379/3";

  @IsString()
  celery_result_backend: string = process.env.REDIS_URL || "redis://localhost:6379/3";

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 4)
  celery_workers: number = parseInt(process.env.CELERY_WORKERS || '4');

  // ========== TOOL CALLING ==========
  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 30)
  tool_call_timeout: number = parseInt(process.env.TOOL_CALL_TIMEOUT || '30');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 50)
  max_tool_calls_per_request: number = parseInt(process.env.MAX_TOOL_CALLS_PER_REQUEST || '50');

  // ========== ERROR HANDLING ==========
  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 3)
  max_retry_attempts: number = parseInt(process.env.MAX_RETRY_ATTEMPTS || '3');

  @IsNumber()
  @Transform(({ value }) => parseFloat(value) || 1.0)
  retry_delay: number = parseFloat(process.env.RETRY_DELAY || '1.0');

  @IsNumber()
  @Transform(({ value }) => parseInt(value) || 1000)
  error_context_window: number = parseInt(process.env.ERROR_CONTEXT_WINDOW || '1000');

  constructor() {
    // Initialize required environment variables
    this.database_url = process.env.DATABASE_URL || '';
    this.redis_url = process.env.REDIS_URL || '';
    this.perplexity_api_key = process.env.PERPLEXITY_API_KEY || '';

    // Validate required fields
    this.validateRequiredFields();
  }

  // ========== VALIDATION METHODS ==========

  /**
   * Validate environment value
   */
  private validateEnvironment(value: string): string {
    const validEnvironments = ["development", "staging", "production"];
    if (!validEnvironments.includes(value)) {
      throw new Error(`Environment must be one of ${validEnvironments.join(', ')}`);
    }
    return value;
  }

  /**
   * Validate log level value
   */
  private validateLogLevel(value: string): string {
    const validLevels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"];
    const upperValue = value.toUpperCase();
    if (!validLevels.includes(upperValue)) {
      throw new Error(`Log level must be one of ${validLevels.join(', ')}`);
    }
    return upperValue;
  }

  /**
   * Validate database URL format
   */
  private validateDatabaseUrl(value: string): string {
    if (!value.startsWith("postgresql://")) {
      throw new Error("Database URL must be a PostgreSQL connection string");
    }
    return value;
  }

  /**
   * Validate Redis URL format
   */
  private validateRedisUrl(value: string): string {
    if (!value.startsWith("redis://")) {
      throw new Error("Redis URL must be a valid Redis connection string");
    }
    return value;
  }

  /**
   * Validate required fields are present
   */
  private validateRequiredFields(): void {
    const requiredFields = [
      { field: 'database_url', value: this.database_url },
      { field: 'redis_url', value: this.redis_url },
      { field: 'perplexity_api_key', value: this.perplexity_api_key }
    ];

    const missingFields = requiredFields
      .filter(({ value }) => !value || value.trim() === '')
      .map(({ field }) => field);

    if (missingFields.length > 0) {
      throw new Error(`Missing required environment variables: ${missingFields.join(', ')}`);
    }
  }

  // ========== COMPUTED PROPERTIES ==========

  /**
   * Check if running in production environment
   */
  get isProduction(): boolean {
    return this.environment === "production";
  }

  /**
   * Check if running in development environment
   */
  get isDevelopment(): boolean {
    return this.environment === "development";
  }

  /**
   * Get database configuration object
   */
  get databaseConfig(): DatabaseConfig {
    return {
      url: this.database_url,
      pool_size: this.database_pool_size,
      max_overflow: this.database_max_overflow,
      echo: this.isDevelopment,
    };
  }

  /**
   * Get Redis configuration object
   */
  get redisConfig(): RedisConfig {
    return {
      url: this.redis_url,
      max_connections: this.redis_max_connections,
      decode_responses: true,
    };
  }

  /**
   * Get Perplexity API configuration object
   */
  get perplexityConfig(): PerplexityConfig {
    return {
      api_key: this.perplexity_api_key,
      model: this.perplexity_model,
      max_tokens: this.perplexity_max_tokens,
      temperature: this.perplexity_temperature,
    };
  }

  /**
   * Get Celery configuration object
   */
  get celeryConfig(): CeleryConfig {
    return {
      broker_url: this.celery_broker_url,
      result_backend: this.celery_result_backend,
      task_serializer: "json",
      accept_content: ["json"],
      result_serializer: "json",
      timezone: "UTC",
      enable_utc: true,
      worker_concurrency: this.celery_workers,
      worker_prefetch_multiplier: 1,
      task_acks_late: true,
      worker_max_tasks_per_child: 1000,
    };
  }

  /**
   * Validate the entire settings object
   */
  async validate(): Promise<void> {
    await validateOrReject(this);
  }
}

// ========== CONFIGURATION INTERFACES ==========

export interface DatabaseConfig {
  url: string;
  pool_size: number;
  max_overflow: number;
  echo: boolean;
}

export interface RedisConfig {
  url: string;
  max_connections: number;
  decode_responses: boolean;
}

export interface PerplexityConfig {
  api_key: string;
  model: string;
  max_tokens: number;
  temperature: number;
}

export interface CeleryConfig {
  broker_url: string;
  result_backend: string;
  task_serializer: string;
  accept_content: string[];
  result_serializer: string;
  timezone: string;
  enable_utc: boolean;
  worker_concurrency: number;
  worker_prefetch_multiplier: number;
  task_acks_late: boolean;
  worker_max_tasks_per_child: number;
}

// ========== SINGLETON PATTERN ==========

let _settingsInstance: Settings | null = null;

/**
 * Get cached settings instance (singleton pattern)
 */
export function getSettings(): Settings {
  if (!_settingsInstance) {
    _settingsInstance = new Settings();
  }
  return _settingsInstance;
}

/**
 * Reset settings instance (useful for testing)
 */
export function resetSettings(): void {
  _settingsInstance = null;
}

// Global settings instance
export const settings = getSettings();

// Export default settings instance
export default settings;