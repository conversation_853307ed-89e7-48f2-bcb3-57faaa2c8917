/**
 * Tool Service for managing structured tool registry - TypeScript version
 * Implements Factor 4: Tools Are Just Structured Outputs
 */

import {
  ToolDefinition,
  ToolCall,
  ToolStatistics,
  ToolExecutionContext,
  BaseMetadata,
  Logger,
  DatabaseQueryParams,
  DatabaseResult,
  AnyFunction
} from './types';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

// Mock database service - replace with actual database implementation
const mockDbService = {
  async executeQuery(query: string, params?: DatabaseQueryParams): Promise<DatabaseResult[]> {
    // Mock implementation
    return [];
  },
  async executeSingleQuery(query: string, params?: DatabaseQueryParams): Promise<DatabaseResult | null> {
    // Mock implementation
    return null;
  },
  async executeCommand(query: string, params?: DatabaseQueryParams): Promise<void> {
    // Mock implementation
  }
};

// Mock cache manager - replace with actual implementation
const mockCacheManager = {
  async invalidatePattern(pattern: string): Promise<void> {
    // Mock implementation
  },
  redis: {
    async get(key: string): Promise<any> {
      // Mock implementation
      return null;
    },
    async set(key: string, value: any, options?: { ex?: number }): Promise<void> {
      // Mock implementation
    }
  }
};

export class ToolService {
  private readonly registeredTools: Map<string, AnyFunction>;
  private readonly toolCacheTtl: number;

  constructor() {
    this.registeredTools = new Map();
    this.toolCacheTtl = 1800; // 30 minutes
  }

  /**
   * Register a new tool in the registry
   * Implements structured tool management for Factor 4
   */
  async registerTool(
    name: string,
    description: string,
    schema: ToolDefinition['schema'],
    implementation: AnyFunction,
    category: string = "general",
    version: string = "1.0.0"
  ): Promise<string> {
    logger.info("Registering tool", { name, category, version });

    try {
      // Validate tool implementation
      if (typeof implementation !== 'function') {
        throw new Error("Tool implementation must be a function");
      }

      // Store tool definition in database (mock implementation)
      const toolData = {
        name,
        description,
        schema,
        implementation: `${implementation.name || 'anonymous'}`,
        category,
        version,
        is_active: true
      };

      // Mock database storage
      const toolId = `tool_${Date.now()}`;

      // Register in memory for fast access
      this.registeredTools.set(name, implementation);

      // Clear tool cache
      await mockCacheManager.invalidatePattern("tools:*");

      logger.info("Tool registered successfully", { name, tool_id: toolId });

      return toolId;

    } catch (error) {
      logger.error("Failed to register tool", { name, error: String(error) });
      throw error;
    }
  }

  /**
   * Get a tool implementation by name
   */
  async getTool(name: string): Promise<AnyFunction | null> {
    // Check in-memory cache first
    if (this.registeredTools.has(name)) {
      return this.registeredTools.get(name) || null;
    }

    // Load from database
    const toolDef = await this.getToolDefinition(name);
    if (!toolDef) {
      return null;
    }

    // For this mock implementation, we'll return a placeholder function
    // In a real implementation, you would dynamically import the tool
    const mockImplementation = async (input: BaseMetadata) => {
      return { result: `Mock execution of tool ${name}`, input };
    };

    // Cache for future use
    this.registeredTools.set(name, mockImplementation);
    return mockImplementation;
  }

  /**
   * Get tool definition from database
   */
  async getToolDefinition(name: string): Promise<ToolDefinition | null> {
    const cacheKey = `tools:definition:${name}`;

    // Check cache
    const cachedDef = await mockCacheManager.redis.get(cacheKey);
    if (cachedDef) {
      return cachedDef;
    }

    // Mock database query
    const toolDef: ToolDefinition | null = null; // Would query actual database

    if (toolDef) {
      // Cache the result
      await mockCacheManager.redis.set(cacheKey, toolDef, { ex: this.toolCacheTtl });
    }

    return toolDef;
  }

  /**
   * Get all active tools, optionally filtered by category
   */
  async getActiveTools(category?: string): Promise<ToolDefinition[]> {
    const cacheKey = `tools:active:${category || 'all'}`;

    // Check cache
    const cachedTools = await mockCacheManager.redis.get(cacheKey);
    if (cachedTools) {
      return cachedTools;
    }

    // Mock database query
    const tools: ToolDefinition[] = []; // Would query actual database

    // Cache the result
    await mockCacheManager.redis.set(cacheKey, tools, { ex: this.toolCacheTtl });

    return tools;
  }

  /**
   * Execute a tool with structured input/output
   * Implements Factor 4: Structured tool execution
   */
  async executeTool(
    name: string,
    inputData: BaseMetadata,
    executionContext?: ToolExecutionContext
  ): Promise<BaseMetadata> {
    const startTime = new Date();

    logger.info("Executing tool", { name, input_keys: Object.keys(inputData) });

    try {
      // Get tool implementation
      const toolFunc = await this.getTool(name);
      if (!toolFunc) {
        throw new Error(`Tool '${name}' not found or not active`);
      }

      // Get tool definition for validation
      const toolDef = await this.getToolDefinition(name);
      if (!toolDef) {
        throw new Error(`Tool definition for '${name}' not found`);
      }

      // Validate input against schema
      await this.validateToolInput(inputData, toolDef.schema);

      // Execute the tool
      let outputData: any;
      if (this.isAsyncFunction(toolFunc)) {
        outputData = await toolFunc(inputData);
      } else {
        outputData = toolFunc(inputData);
      }

      // Ensure output is structured
      if (typeof outputData !== 'object' || outputData === null) {
        outputData = { result: outputData };
      }

      const executionTime = (new Date().getTime() - startTime.getTime()) / 1000;

      // Create tool call record
      const toolCall: ToolCall = {
        tool_name: name,
        input_data: inputData,
        output_data: outputData,
        execution_time: executionTime,
        timestamp: startTime.toISOString(),
        success: true
      };

      // Log the tool call
      await this.logToolCall(toolCall);

      logger.info("Tool executed successfully", { name, execution_time: executionTime });

      return outputData;

    } catch (error) {
      const executionTime = (new Date().getTime() - startTime.getTime()) / 1000;

      // Create error tool call record
      const toolCall: ToolCall = {
        tool_name: name,
        input_data: inputData,
        output_data: {},
        execution_time: executionTime,
        timestamp: startTime.toISOString(),
        success: false,
        error_info: String(error)
      };

      await this.logToolCall(toolCall);

      logger.error("Tool execution failed", {
        name,
        error: String(error),
        execution_time: executionTime
      });

      throw error;
    }
  }

  /**
   * Validate tool input against schema
   */
  private async validateToolInput(inputData: BaseMetadata, schema: ToolDefinition['schema']): Promise<void> {
    // Basic validation - can be enhanced with jsonschema
    const requiredFields = schema.required || [];
    const properties = schema.properties || {};

    // Check required fields
    for (const field of requiredFields) {
      if (!(field in inputData)) {
        throw new Error(`Required field '${field}' missing from input`);
      }
    }

    // Check field types (basic validation)
    for (const [field, value] of Object.entries(inputData)) {
      if (field in properties) {
        const expectedType = properties[field].type;
        if (expectedType && !this.validateType(value, expectedType)) {
          throw new Error(`Field '${field}' has invalid type. Expected ${expectedType}`);
        }
      }
    }
  }

  /**
   * Basic type validation
   */
  private validateType(value: any, expectedType: string): boolean {
    const typeMapping: Record<string, string> = {
      "string": "string",
      "integer": "number",
      "number": "number",
      "boolean": "boolean",
      "array": "object",
      "object": "object"
    };

    const expectedJsType = typeMapping[expectedType];
    if (expectedJsType) {
      if (expectedType === "array") {
        return Array.isArray(value);
      } else if (expectedType === "integer") {
        return typeof value === "number" && Number.isInteger(value);
      } else {
        return typeof value === expectedJsType;
      }
    }

    return true; // Unknown type, pass validation
  }

  /**
   * Check if function is async
   */
  private isAsyncFunction(func: AnyFunction): boolean {
    return func.constructor.name === 'AsyncFunction';
  }

  /**
   * Log tool call for monitoring and debugging
   */
  private async logToolCall(toolCall: ToolCall): Promise<void> {
    try {
      // Mock database storage
      logger.debug("Tool call logged", {
        tool_name: toolCall.tool_name,
        success: toolCall.success,
        execution_time: toolCall.execution_time
      });

    } catch (error) {
      logger.error("Failed to log tool call", { error: String(error) });
    }
  }

  /**
   * Get tool usage statistics
   */
  async getToolStatistics(name?: string): Promise<ToolStatistics | { tools: ToolStatistics[] }> {
    try {
      if (name) {
        // Statistics for specific tool (mock implementation)
        const stats: ToolStatistics = {
          tool_name: name,
          total_calls: 0,
          successful_calls: 0,
          failed_calls: 0,
          avg_execution_time: 0,
          success_rate: 0
        };

        return stats;
      } else {
        // Overall statistics (mock implementation)
        const stats: ToolStatistics[] = [];
        return { tools: stats };
      }

    } catch (error) {
      logger.error("Failed to get tool statistics", { error: String(error) });
      return { error: String(error) } as any;
    }
  }

  /**
   * Deactivate a tool
   */
  async deactivateTool(name: string): Promise<boolean> {
    try {
      // Mock database update
      const success = true; // Would update actual database

      if (success) {
        // Remove from memory cache
        this.registeredTools.delete(name);

        // Clear cache
        await mockCacheManager.invalidatePattern("tools:*");

        logger.info("Tool deactivated", { name });
        return true;
      }

      return false;

    } catch (error) {
      logger.error("Failed to deactivate tool", { name, error: String(error) });
      return false;
    }
  }

  /**
   * Get list of registered tool names
   */
  getRegisteredToolNames(): string[] {
    return Array.from(this.registeredTools.keys());
  }

  /**
   * Get service information
   */
  getServiceInfo(): { version: string; registered_tools: number; cache_ttl: number } {
    return {
      version: "1.0.0",
      registered_tools: this.registeredTools.size,
      cache_ttl: this.toolCacheTtl
    };
  }
}
