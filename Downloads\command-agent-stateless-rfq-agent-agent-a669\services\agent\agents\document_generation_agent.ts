/**
 * Factor 10: Small, Focused Agents - Document Generation Agent
 * Specialized agent for RFQ document creation and customization
 * Converted from Python to TypeScript
 */

import { Logger } from 'winston';
import { IsString, IsOptional, IsArray, IsNumber, IsEnum, Min, Max, IsDateString } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { StateGraph, CompiledStateGraph } from './rfq_agent';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

// ========== DATA CLASSES ==========

export class DocumentRequirements {
  @IsString()
  rfq_type!: string;

  @IsString()
  category!: string;

  @IsString()
  region!: string;

  @IsOptional()
  @IsString()
  budget_range?: string;

  @IsString()
  urgency: string = 'medium';

  @IsString()
  department: string = 'procurement';

  @IsArray()
  @IsString({ each: true })
  compliance_requirements: string[] = [];

  @IsArray()
  @IsString({ each: true })
  evaluation_criteria: string[] = [];

  @IsArray()
  @IsString({ each: true })
  custom_specifications: string[] = [];
}

export class RFQDocument {
  @IsString()
  document_id!: string;

  @IsString()
  title!: string;

  @IsString()
  description!: string;

  @IsArray()
  @IsString({ each: true })
  specifications!: string[];

  @IsArray()
  @IsString({ each: true })
  evaluation_criteria!: string[];

  @IsArray()
  @IsString({ each: true })
  submission_requirements!: string[];

  @IsString()
  delivery_requirements!: string;

  @IsString()
  deadline!: string;

  @IsArray()
  @IsString({ each: true })
  legal_terms!: string[];

  @IsArray()
  @IsString({ each: true })
  compliance_notes!: string[];

  @IsOptional()
  contact_information?: Record<string, string>;

  @IsString()
  document_version: string = '1.0';

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : new Date())
  created_at?: Date = new Date();
}

export class DocumentGenerationState {
  @Type(() => DocumentRequirements)
  requirements!: DocumentRequirements;

  // Generated components
  @IsOptional()
  document_structure?: Record<string, any>;

  @IsOptional()
  content_sections?: Record<string, string>;

  @IsOptional()
  @IsString()
  compliance_section?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  legal_terms?: string[];

  @IsOptional()
  @Type(() => RFQDocument)
  final_document?: RFQDocument;

  // Quality metrics
  @IsNumber()
  @Min(0)
  @Max(1)
  document_quality_score: number = 0.0;

  @IsNumber()
  @Min(0)
  @Max(1)
  compliance_score: number = 0.0;

  @IsNumber()
  @Min(0)
  @Max(1)
  completeness_score: number = 0.0;
}

// ========== MOCK SERVICES ==========

interface PerplexityService {
  research_with_sources(query: string, context: string): Promise<{ content: string; sources: string[] }>;
  generate_response(prompt: string, context: string): Promise<string>;
}

interface MemoryService {
  store_conversation_memory(agent_id: string, content: string, memory_type: string, metadata: Record<string, any>): Promise<void>;
}

interface ErrorManager {
  compact_error_for_context(error: any, context: Record<string, any>, execution_id: string, step_name: string): Promise<string>;
}

// Mock implementations
const mockPerplexityService: PerplexityService = {
  async research_with_sources(query: string, context: string) {
    return {
      content: `Mock research results for: ${query}`,
      sources: ['source1.com', 'source2.com']
    };
  },
  async generate_response(prompt: string, context: string) {
    return `Mock response for: ${prompt.substring(0, 100)}...`;
  }
};

const mockMemoryService: MemoryService = {
  async store_conversation_memory(agent_id: string, content: string, memory_type: string, metadata: Record<string, any>) {
    logger.info('Storing memory', { agent_id, memory_type, content: content.substring(0, 100), metadata });
  }
};

const mockErrorManager: ErrorManager = {
  async compact_error_for_context(error: any, context: Record<string, any>, execution_id: string, step_name: string) {
    return `Error in ${step_name}: ${String(error)}`;
  }
};

// ========== DOCUMENT GENERATION AGENT ==========

export class DocumentGenerationAgent {
  private perplexity: PerplexityService;
  private memory: MemoryService;
  private errorManager: ErrorManager;
  private agentId = 'document_generation_agent';
  private workflow: CompiledStateGraph<DocumentGenerationState>;

  constructor() {
    this.perplexity = mockPerplexityService;
    this.memory = mockMemoryService;
    this.errorManager = mockErrorManager;
    this.workflow = this.buildWorkflow();
  }

  /**
   * Build document generation workflow
   */
  private buildWorkflow(): CompiledStateGraph<DocumentGenerationState> {
    const workflow = new StateGraph<DocumentGenerationState>();
    
    workflow.addNode('analyze_requirements', this.analyzeRequirements.bind(this));
    workflow.addNode('generate_structure', this.generateDocumentStructure.bind(this));
    workflow.addNode('create_content', this.createContentSections.bind(this));
    workflow.addNode('add_compliance', this.addComplianceRequirements.bind(this));
    workflow.addNode('finalize_document', this.finalizeDocument.bind(this));
    
    workflow.setEntryPoint('analyze_requirements');
    workflow.addEdge('analyze_requirements', 'generate_structure');
    workflow.addEdge('generate_structure', 'create_content');
    workflow.addEdge('create_content', 'add_compliance');
    workflow.addEdge('add_compliance', 'finalize_document');
    workflow.addEdge('finalize_document', 'END');
    
    return workflow.compile();
  }

  // ========== WORKFLOW NODE IMPLEMENTATIONS ==========

  /**
   * Analyze and enhance document requirements
   */
  private async analyzeRequirements(state: DocumentGenerationState): Promise<DocumentGenerationState> {
    try {
      // Use Perplexity to research best practices for RFQ documents
      const requirementsQuery = `
      Research best practices for RFQ documents in ${state.requirements.category} procurement.
      
      Include:
      1. Essential sections and structure for professional RFQs
      2. Industry-specific requirements and specifications
      3. Standard evaluation criteria for ${state.requirements.category}
      4. Compliance requirements for ${state.requirements.region}
      5. Legal terms and conditions best practices
      6. Submission format and deadline considerations
      
      Focus on creating comprehensive, professional procurement documents.
      `;
      
      const requirementsResearch = await this.perplexity.research_with_sources(
        requirementsQuery,
        'rfq_document_best_practices'
      );
      
      // Store requirements analysis
      await this.memory.store_conversation_memory(
        this.agentId,
        `RFQ requirements analysis: ${requirementsResearch.content.substring(0, 500)}...`,
        'document_requirements',
        {
          category: state.requirements.category,
          region: state.requirements.region
        }
      );
      
      logger.info(`Analyzed requirements for ${state.requirements.category} RFQ`);
      return state;
      
    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: state },
        `document_gen_${Date.now()}`,
        'analyze_requirements'
      );
      logger.error(`Requirements analysis failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Generate optimal document structure
   */
  private async generateDocumentStructure(state: DocumentGenerationState): Promise<DocumentGenerationState> {
    try {
      // Create document structure based on requirements
      const currentDate = new Date();
      const deadlineDate = new Date(currentDate.getTime() + (14 * 24 * 60 * 60 * 1000)); // 14 days from now

      const documentStructure = {
        header: {
          title: `Request for Quotation - ${state.requirements.category}`,
          rfq_number: `RFQ-${currentDate.toISOString().slice(0, 10).replace(/-/g, '')}-${state.requirements.category.substring(0, 3).toUpperCase()}`,
          issue_date: currentDate.toISOString().slice(0, 10),
          deadline: deadlineDate.toISOString().slice(0, 10)
        },
        sections: [
          'executive_summary',
          'project_overview',
          'technical_specifications',
          'commercial_requirements',
          'evaluation_criteria',
          'submission_requirements',
          'terms_and_conditions',
          'contact_information'
        ],
        appendices: [
          'compliance_checklist',
          'reference_documents',
          'submission_templates'
        ]
      };

      state.document_structure = documentStructure;

      logger.info('Generated document structure');
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: state },
        `document_gen_${Date.now()}`,
        'generate_structure'
      );
      logger.error(`Structure generation failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Create content for each document section
   */
  private async createContentSections(state: DocumentGenerationState): Promise<DocumentGenerationState> {
    try {
      // Generate content using Perplexity for professional language
      const contentQuery = `
      Generate professional RFQ content for ${state.requirements.category} procurement.

      Create detailed content for:
      1. Executive Summary - Brief overview of procurement needs
      2. Project Overview - Detailed description of requirements
      3. Technical Specifications - Specific technical requirements
      4. Commercial Requirements - Pricing, payment, delivery terms
      5. Evaluation Criteria - How proposals will be evaluated
      6. Submission Requirements - Format, deadline, contact details

      Use professional procurement language suitable for ${state.requirements.region}.
      Budget context: ${state.requirements.budget_range || 'To be determined'}
      Urgency: ${state.requirements.urgency}
      `;

      const contentResearch = await this.perplexity.generate_response(
        contentQuery,
        'rfq_content_generation'
      );

      // Structure the generated content
      const contentSections = {
        executive_summary: this.extractExecutiveSummary(contentResearch),
        project_overview: this.extractProjectOverview(contentResearch, state.requirements),
        technical_specifications: this.generateTechnicalSpecs(state.requirements),
        commercial_requirements: this.generateCommercialTerms(state.requirements),
        evaluation_criteria: this.generateEvaluationCriteria(state.requirements),
        submission_requirements: this.generateSubmissionRequirements(state.requirements)
      };

      state.content_sections = contentSections;

      logger.info('Generated content sections');
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: state },
        `document_gen_${Date.now()}`,
        'create_content'
      );
      logger.error(`Content creation failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Add compliance and legal requirements
   */
  private async addComplianceRequirements(state: DocumentGenerationState): Promise<DocumentGenerationState> {
    try {
      // Research compliance requirements
      const complianceQuery = `
      Research compliance requirements for ${state.requirements.category} procurement in ${state.requirements.region}.

      Include:
      1. Legal and regulatory compliance requirements
      2. Industry-specific certifications and standards
      3. Environmental and sustainability requirements
      4. Quality assurance and testing requirements
      5. Documentation and reporting obligations
      6. Insurance and liability requirements

      Provide specific compliance language for RFQ documents.
      `;

      const complianceResearch = await this.perplexity.research_with_sources(
        complianceQuery,
        'compliance_requirements'
      );

      // Generate compliance section
      const complianceSection = this.generateComplianceSection(
        complianceResearch.content,
        state.requirements
      );

      // Generate legal terms
      const legalTerms = this.generateLegalTerms(state.requirements);

      state.compliance_section = complianceSection;
      state.legal_terms = legalTerms;

      // Calculate compliance score
      state.compliance_score = this.calculateComplianceScore(
        state.requirements.compliance_requirements,
        complianceSection
      );

      logger.info(`Added compliance requirements with score: ${state.compliance_score}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: state },
        `document_gen_${Date.now()}`,
        'add_compliance'
      );
      logger.error(`Compliance addition failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Finalize the complete RFQ document
   */
  private async finalizeDocument(state: DocumentGenerationState): Promise<DocumentGenerationState> {
    try {
      // Create final document
      const currentDate = new Date();
      const documentId = `RFQ-${currentDate.toISOString().slice(0, 16).replace(/[-:T]/g, '')}-${state.requirements.category.substring(0, 3).toUpperCase()}`;

      const finalDocument = new RFQDocument();
      finalDocument.document_id = documentId;
      finalDocument.title = state.document_structure?.header?.title || `RFQ - ${state.requirements.category}`;
      finalDocument.description = state.content_sections?.project_overview || '';
      finalDocument.specifications = this.compileSpecifications(state);
      finalDocument.evaluation_criteria = this.compileEvaluationCriteria(state);
      finalDocument.submission_requirements = this.compileSubmissionRequirements(state);
      finalDocument.delivery_requirements = this.generateDeliveryRequirements(state.requirements);
      finalDocument.deadline = state.document_structure?.header?.deadline || '';
      finalDocument.legal_terms = state.legal_terms || [];
      finalDocument.compliance_notes = state.compliance_section ? [state.compliance_section] : [];
      finalDocument.contact_information = this.generateContactInfo(state.requirements);

      // Calculate quality scores
      state.completeness_score = this.calculateCompletenessScore(finalDocument);
      state.document_quality_score = (
        state.compliance_score * 0.4 +
        state.completeness_score * 0.6
      );

      state.final_document = finalDocument;

      // Store final document
      await this.memory.store_conversation_memory(
        this.agentId,
        `Generated RFQ document: ${JSON.stringify(finalDocument, null, 2)}`,
        'generated_documents',
        {
          document_id: documentId,
          category: state.requirements.category,
          quality_score: state.document_quality_score
        }
      );

      logger.info(`Finalized document ${documentId} with quality score: ${state.document_quality_score}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: state },
        `document_gen_${Date.now()}`,
        'finalize_document'
      );
      logger.error(`Document finalization failed: ${errorContext}`);
      return state;
    }
  }

  // ========== HELPER METHODS ==========

  /**
   * Extract executive summary from generated content
   */
  private extractExecutiveSummary(content: string): string {
    return 'This RFQ outlines requirements for procurement of specified goods/services.';
  }

  /**
   * Extract project overview from content
   */
  private extractProjectOverview(content: string, requirements: DocumentRequirements): string {
    return `Procurement project for ${requirements.category} with ${requirements.urgency} priority.`;
  }

  /**
   * Generate technical specifications
   */
  private generateTechnicalSpecs(requirements: DocumentRequirements): string {
    const baseSpecs = [
      'Items must meet all specified quality standards',
      'Compliance with applicable industry regulations',
      'Warranty and support as per requirements'
    ];
    const allSpecs = [...baseSpecs, ...requirements.custom_specifications];
    return allSpecs.join('\n');
  }

  /**
   * Generate commercial terms
   */
  private generateCommercialTerms(requirements: DocumentRequirements): string {
    let terms = `Budget range: ${requirements.budget_range || 'To be determined'}. `;
    terms += 'Payment terms: Net 30 days. ';
    terms += `Delivery required in ${requirements.region}.`;
    return terms;
  }

  /**
   * Generate evaluation criteria
   */
  private generateEvaluationCriteria(requirements: DocumentRequirements): string {
    const defaultCriteria = [
      'Price competitiveness (40%)',
      'Technical compliance (30%)',
      'Delivery capability (20%)',
      'Vendor experience (10%)'
    ];
    const criteria = requirements.evaluation_criteria.length > 0 ? requirements.evaluation_criteria : defaultCriteria;
    return criteria.join('\n');
  }

  /**
   * Generate submission requirements
   */
  private generateSubmissionRequirements(requirements: DocumentRequirements): string {
    const submissionReqs = [
      'Complete technical proposal',
      'Commercial pricing proposal',
      'Company credentials and references',
      'Compliance documentation',
      'Signed terms and conditions'
    ];
    return submissionReqs.join('\n');
  }

  /**
   * Generate compliance section
   */
  private generateComplianceSection(researchContent: string, requirements: DocumentRequirements): string {
    return `All suppliers must comply with applicable regulations in ${requirements.region}.`;
  }

  /**
   * Generate legal terms
   */
  private generateLegalTerms(requirements: DocumentRequirements): string[] {
    return [
      'Standard terms and conditions apply',
      'Confidentiality agreement required',
      'Liability limitations as per contract',
      'Governing law and jurisdiction clauses'
    ];
  }

  /**
   * Compile all specifications
   */
  private compileSpecifications(state: DocumentGenerationState): string[] {
    const specs = this.generateTechnicalSpecs(state.requirements);
    return specs.split('\n').filter(spec => spec.trim().length > 0);
  }

  /**
   * Compile evaluation criteria
   */
  private compileEvaluationCriteria(state: DocumentGenerationState): string[] {
    const criteria = this.generateEvaluationCriteria(state.requirements);
    return criteria.split('\n').filter(criterion => criterion.trim().length > 0);
  }

  /**
   * Compile submission requirements
   */
  private compileSubmissionRequirements(state: DocumentGenerationState): string[] {
    const requirements = this.generateSubmissionRequirements(state.requirements);
    return requirements.split('\n').filter(req => req.trim().length > 0);
  }

  /**
   * Generate delivery requirements
   */
  private generateDeliveryRequirements(requirements: DocumentRequirements): string {
    return `Delivery to ${requirements.region} within agreed timeframe.`;
  }

  /**
   * Generate contact information
   */
  private generateContactInfo(requirements: DocumentRequirements): Record<string, string> {
    return {
      department: requirements.department,
      email: `${requirements.department}@company.com`,
      phone: '******-0123'
    };
  }

  /**
   * Calculate compliance score
   */
  private calculateComplianceScore(requiredCompliance: string[], section: string): number {
    if (requiredCompliance.length === 0) {
      return 1.0;
    }
    return 0.8; // Mock score
  }

  /**
   * Calculate document completeness score
   */
  private calculateCompletenessScore(document: RFQDocument): number {
    const requiredFields = 8;
    const completedFields = [
      !!document.title,
      !!document.description,
      !!document.specifications && document.specifications.length > 0,
      !!document.evaluation_criteria && document.evaluation_criteria.length > 0,
      !!document.submission_requirements && document.submission_requirements.length > 0,
      !!document.delivery_requirements,
      !!document.deadline,
      !!document.contact_information && Object.keys(document.contact_information).length > 0
    ].filter(Boolean).length;

    return completedFields / requiredFields;
  }

  // ========== PUBLIC API ==========

  /**
   * Main entry point for document generation
   */
  async generateRfqDocument(requirements: DocumentRequirements): Promise<Record<string, any>> {
    try {
      const state = new DocumentGenerationState();
      state.requirements = requirements;

      const finalState = await this.workflow.invoke(state);

      if (finalState.final_document) {
        return {
          success: true,
          document: {
            document_id: finalState.final_document.document_id,
            title: finalState.final_document.title,
            description: finalState.final_document.description,
            specifications: finalState.final_document.specifications,
            evaluation_criteria: finalState.final_document.evaluation_criteria,
            submission_requirements: finalState.final_document.submission_requirements,
            delivery_requirements: finalState.final_document.delivery_requirements,
            deadline: finalState.final_document.deadline,
            legal_terms: finalState.final_document.legal_terms,
            compliance_notes: finalState.final_document.compliance_notes,
            contact_information: finalState.final_document.contact_information,
            document_version: finalState.final_document.document_version,
            created_at: finalState.final_document.created_at
          },
          quality_score: finalState.document_quality_score,
          compliance_score: finalState.compliance_score,
          completeness_score: finalState.completeness_score,
          agent_id: this.agentId,
          timestamp: new Date().toISOString()
        };
      } else {
        return {
          success: false,
          error: 'Document generation failed',
          agent_id: this.agentId,
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { requirements: requirements },
        `document_gen_${Date.now()}`,
        'generate_rfq_document'
      );

      return {
        success: false,
        error: errorContext,
        agent_id: this.agentId,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get agent capabilities
   */
  getAgentCapabilities(): Record<string, any> {
    return {
      agent_id: this.agentId,
      agent_type: 'document_generation',
      capabilities: [
        'rfq_generation',
        'compliance_integration',
        'professional_formatting',
        'content_customization',
        'quality_assessment'
      ],
      supported_document_types: [
        'rfq_documents',
        'procurement_specifications',
        'compliance_checklists',
        'evaluation_criteria'
      ],
      workflow_stages: [
        'requirements_analysis',
        'structure_generation',
        'content_creation',
        'compliance_integration',
        'document_finalization'
      ],
      quality_metrics: {
        document_quality_scoring: 'Comprehensive quality assessment',
        compliance_scoring: 'Regulatory compliance evaluation',
        completeness_scoring: 'Document completeness validation'
      },
      description: 'Specialized agent for RFQ document generation and customization',
      version: '1.0.0',
      factor_compliance: ['Factor 10: Small, Focused Agents'],
      execution_time: '2-5 minutes for complete document generation',
      output_formats: ['Structured JSON', 'Professional RFQ format']
    };
  }

  /**
   * Validate document requirements
   */
  validateRequirements(requirements: DocumentRequirements): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!requirements.rfq_type || requirements.rfq_type.trim().length === 0) {
      errors.push('RFQ type is required');
    }

    if (!requirements.category || requirements.category.trim().length === 0) {
      errors.push('Category is required');
    }

    if (!requirements.region || requirements.region.trim().length === 0) {
      errors.push('Region is required');
    }

    if (!['low', 'medium', 'high', 'urgent'].includes(requirements.urgency)) {
      errors.push('Urgency must be one of: low, medium, high, urgent');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate document preview
   */
  async generateDocumentPreview(requirements: DocumentRequirements): Promise<Record<string, any>> {
    try {
      const validation = this.validateRequirements(requirements);
      if (!validation.valid) {
        return {
          success: false,
          errors: validation.errors,
          agent_id: this.agentId
        };
      }

      // Generate a quick preview without full workflow
      const currentDate = new Date();
      const deadlineDate = new Date(currentDate.getTime() + (14 * 24 * 60 * 60 * 1000));

      const preview = {
        title: `Request for Quotation - ${requirements.category}`,
        rfq_number: `RFQ-${currentDate.toISOString().slice(0, 10).replace(/-/g, '')}-${requirements.category.substring(0, 3).toUpperCase()}`,
        issue_date: currentDate.toISOString().slice(0, 10),
        deadline: deadlineDate.toISOString().slice(0, 10),
        category: requirements.category,
        region: requirements.region,
        urgency: requirements.urgency,
        estimated_sections: 8,
        estimated_completion_time: '3-5 minutes'
      };

      return {
        success: true,
        preview,
        agent_id: this.agentId,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        success: false,
        error: String(error),
        agent_id: this.agentId,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export default
export default DocumentGenerationAgent;
