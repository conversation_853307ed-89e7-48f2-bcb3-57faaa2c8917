/**
 * Embedding Service for generating semantic embeddings - TypeScript version
 * Simple implementation that can be extended with more sophisticated models
 */

import { createHash } from 'crypto';
import { EmbeddingVector, EmbeddingItem, Logger, KeywordWeights, BaseMetadata } from './types';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

export class EmbeddingService {
  private readonly dimension: number;

  constructor(dimension: number = 1536) {
    this.dimension = dimension;
  }

  /**
   * Generate embedding for text
   * This is a simple implementation - in production, you would use
   * a proper embedding model like OpenAI's text-embedding-ada-002
   * or sentence-transformers
   */
  async generateEmbedding(text: string): Promise<EmbeddingVector> {
    logger.debug("Generating embedding", { text_length: text.length });

    try {
      // Simple deterministic embedding based on text content
      // In production, replace with actual embedding model

      // Create a hash-based seed for reproducibility
      const textHash = createHash('md5').update(text).digest('hex');
      const seed = parseInt(textHash.substring(0, 8), 16);
      
      // Generate embedding based on text characteristics
      const embedding = this.generateRandomNormal(0, 0.1, this.dimension, seed);

      // Add some semantic meaning based on keywords
      const keywords: KeywordWeights = {
        'rfq': 0.1, 'request': 0.08, 'procurement': 0.12, 'vendor': 0.09,
        'quote': 0.07, 'supplier': 0.1, 'purchase': 0.08, 'budget': 0.06,
        'urgent': 0.05, 'critical': 0.06, 'laptop': 0.04, 'desktop': 0.04,
        'furniture': 0.04, 'it': 0.05, 'technology': 0.05, 'market': 0.07,
        'price': 0.06, 'cost': 0.05, 'delivery': 0.04, 'quality': 0.05
      };

      const textLower = text.toLowerCase();
      for (const [keyword, weight] of Object.entries(keywords)) {
        if (textLower.includes(keyword)) {
          // Modify embedding based on keyword presence
          const keywordSeed = this.hashString(keyword) % 1000;
          const keywordVector = this.generateRandomNormal(0, weight, this.dimension, keywordSeed);
          
          for (let i = 0; i < embedding.length; i++) {
            embedding[i] += keywordVector[i];
          }
        }
      }

      // Normalize the embedding
      const norm = this.vectorNorm(embedding);
      if (norm > 0) {
        for (let i = 0; i < embedding.length; i++) {
          embedding[i] /= norm;
        }
      }

      return embedding;

    } catch (error) {
      logger.error("Failed to generate embedding", { 
        error: String(error), 
        text_length: text.length 
      });
      // Return zero vector as fallback
      return new Array(this.dimension).fill(0.0);
    }
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateBatchEmbeddings(texts: string[]): Promise<EmbeddingVector[]> {
    logger.info("Generating batch embeddings", { batch_size: texts.length });

    const embeddings: EmbeddingVector[] = [];
    for (const text of texts) {
      const embedding = await this.generateEmbedding(text);
      embeddings.push(embedding);
    }

    return embeddings;
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  cosineSimilarity(embedding1: EmbeddingVector, embedding2: EmbeddingVector): number {
    try {
      // Calculate cosine similarity
      const dotProduct = this.dotProduct(embedding1, embedding2);
      const norm1 = this.vectorNorm(embedding1);
      const norm2 = this.vectorNorm(embedding2);

      if (norm1 === 0 || norm2 === 0) {
        return 0.0;
      }

      const similarity = dotProduct / (norm1 * norm2);
      return similarity;

    } catch (error) {
      logger.error("Failed to calculate cosine similarity", { error: String(error) });
      return 0.0;
    }
  }

  /**
   * Find similar embeddings from a list of candidates
   */
  async findSimilarEmbeddings(
    queryEmbedding: EmbeddingVector,
    candidateEmbeddings: EmbeddingItem[],
    threshold: number = 0.7
  ): Promise<EmbeddingItem[]> {
    logger.debug("Finding similar embeddings", {
      candidates_count: candidateEmbeddings.length,
      threshold
    });

    const similarItems: EmbeddingItem[] = [];

    for (const item of candidateEmbeddings) {
      if (!item.embedding || item.embedding.length === 0) {
        continue;
      }

      const similarity = this.cosineSimilarity(queryEmbedding, item.embedding);

      if (similarity >= threshold) {
        similarItems.push({
          ...item,
          similarity
        });
      }
    }

    // Sort by similarity (highest first)
    similarItems.sort((a, b) => (b.similarity || 0) - (a.similarity || 0));

    return similarItems;
  }

  // ========== UTILITY METHODS ==========

  /**
   * Generate random normal distribution values
   */
  private generateRandomNormal(mean: number, stdDev: number, size: number, seed?: number): number[] {
    // Simple Box-Muller transform for normal distribution
    const random = seed ? this.seededRandom(seed) : Math.random;
    const values: number[] = [];

    for (let i = 0; i < size; i += 2) {
      const u1 = random();
      const u2 = random();
      
      const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
      const z1 = Math.sqrt(-2 * Math.log(u1)) * Math.sin(2 * Math.PI * u2);
      
      values.push(mean + stdDev * z0);
      if (i + 1 < size) {
        values.push(mean + stdDev * z1);
      }
    }

    return values.slice(0, size);
  }

  /**
   * Seeded random number generator
   */
  private seededRandom(seed: number): () => number {
    let currentSeed = seed;
    return () => {
      currentSeed = (currentSeed * 9301 + 49297) % 233280;
      return currentSeed / 233280;
    };
  }

  /**
   * Calculate dot product of two vectors
   */
  private dotProduct(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) {
      throw new Error("Vectors must have the same length");
    }

    let sum = 0;
    for (let i = 0; i < vec1.length; i++) {
      sum += vec1[i] * vec2[i];
    }
    return sum;
  }

  /**
   * Calculate vector norm (magnitude)
   */
  private vectorNorm(vector: number[]): number {
    let sum = 0;
    for (const value of vector) {
      sum += value * value;
    }
    return Math.sqrt(sum);
  }

  /**
   * Hash string to number
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Get service information
   */
  getServiceInfo(): { dimension: number; version: string } {
    return {
      dimension: this.dimension,
      version: "1.0.0"
    };
  }
}
