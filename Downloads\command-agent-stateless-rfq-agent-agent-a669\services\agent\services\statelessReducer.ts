/**
 * Factor 12: Make Your Agent a Stateless Reducer - TypeScript version
 * Implements functional programming patterns for stateless agent operations
 */

import {
  ActionType,
  Action,
  AgentState,
  WorkflowStepFunction,
  ServiceCapabilities,
  BaseMetadata,
  Logger
} from './types';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

/**
 * Abstract base class for state reducers
 */
abstract class StateReducer {
  abstract reduce(state: AgentState, action: Action): AgentState;
}

/**
 * Factor 12: Stateless Reducer for RFQ workflows
 * 
 * Pure functional state management with:
 * - Immutable state objects
 * - Pure reducer functions
 * - Predictable state transitions
 * - No side effects
 * - Composable operations
 */
class RFQStateReducer extends StateReducer {
  /**
   * Pure function that returns new state based on current state and action
   * No side effects, no mutations, fully predictable
   */
  reduce(state: AgentState, action: Action): AgentState {
    // Create new state object (immutable pattern)
    const newState: AgentState = {
      ...state,
      last_updated: action.timestamp
    };

    // Apply state transition based on action type
    switch (action.type) {
      case ActionType.INITIALIZE:
        return this.handleInitialize(newState, action);
      case ActionType.UPDATE_CONTEXT:
        return this.handleUpdateContext(newState, action);
      case ActionType.ADD_MEMORY:
        return this.handleAddMemory(newState, action);
      case ActionType.SET_STATUS:
        return this.handleSetStatus(newState, action);
      case ActionType.UPDATE_PROGRESS:
        return this.handleUpdateProgress(newState, action);
      case ActionType.ADD_RESULT:
        return this.handleAddResult(newState, action);
      case ActionType.SET_ERROR:
        return this.handleSetError(newState, action);
      case ActionType.CLEAR_ERROR:
        return this.handleClearError(newState, action);
      case ActionType.MERGE_STATE:
        return this.handleMergeState(newState, action);
      default:
        // Return unchanged state for unknown actions
        return newState;
    }
  }

  private handleInitialize(state: AgentState, action: Action): AgentState {
    return {
      ...state,
      execution_id: action.payload.execution_id || state.execution_id,
      status: 'initialized',
      progress: 0.0,
      context: action.payload.context || {},
      memories: [],
      results: {},
      errors: [],
      metadata: action.payload.metadata || {}
    };
  }

  private handleUpdateContext(state: AgentState, action: Action): AgentState {
    return {
      ...state,
      context: { ...state.context, ...action.payload.context_updates }
    };
  }

  private handleAddMemory(state: AgentState, action: Action): AgentState {
    const newMemory = {
      content: action.payload.content,
      type: action.payload.memory_type || 'general',
      timestamp: action.timestamp.toISOString(),
      metadata: action.payload.metadata || {}
    };

    return {
      ...state,
      memories: [...state.memories, newMemory]
    };
  }

  private handleSetStatus(state: AgentState, action: Action): AgentState {
    return {
      ...state,
      status: action.payload.status || state.status
    };
  }

  private handleUpdateProgress(state: AgentState, action: Action): AgentState {
    const newProgress = action.payload.progress || state.progress;
    return {
      ...state,
      progress: Math.max(0.0, Math.min(1.0, newProgress)) // Clamp to [0,1]
    };
  }

  private handleAddResult(state: AgentState, action: Action): AgentState {
    const resultKey = action.payload.key || 'default';
    const resultValue = action.payload.value;

    return {
      ...state,
      results: { ...state.results, [resultKey]: resultValue }
    };
  }

  private handleSetError(state: AgentState, action: Action): AgentState {
    const errorMessage = action.payload.error || 'Unknown error';
    return {
      ...state,
      errors: [...state.errors, errorMessage],
      status: 'error'
    };
  }

  private handleClearError(state: AgentState, action: Action): AgentState {
    return {
      ...state,
      errors: [],
      status: state.status === 'error' ? 'running' : state.status
    };
  }

  private handleMergeState(state: AgentState, action: Action): AgentState {
    const mergeData = action.payload.state_updates || {};
    return { ...state, ...mergeData };
  }
}

/**
 * Factor 12: Stateless Agent Service
 * 
 * Implements stateless reducer pattern for agent operations:
 * - All operations are pure functions
 * - State is immutable
 * - Actions are the only way to change state
 * - Predictable and testable
 * - Side effects isolated to service layer
 */
export class StatelessAgentService {
  private readonly reducer: RFQStateReducer;
  private readonly serviceId: string;

  constructor() {
    this.reducer = new RFQStateReducer();
    this.serviceId = "stateless_agent_service";
  }

  // ========== PURE FUNCTIONS ==========

  /**
   * Create initial agent state - pure function
   */
  createInitialState(executionId: string, context: BaseMetadata = {}): AgentState {
    return {
      execution_id: executionId,
      status: "initialized",
      progress: 0.0,
      context,
      memories: [],
      results: {},
      errors: [],
      metadata: { created_at: new Date().toISOString() },
      last_updated: new Date()
    };
  }

  /**
   * Apply action to state - pure function
   */
  applyAction(state: AgentState, action: Action): AgentState {
    return this.reducer.reduce(state, action);
  }

  /**
   * Apply sequence of actions to state - pure function
   */
  applyActions(state: AgentState, actions: Action[]): AgentState {
    return actions.reduce(
      (currentState, action) => this.applyAction(currentState, action),
      state
    );
  }

  /**
   * Create action object - pure function
   */
  createAction(actionType: ActionType, payload: BaseMetadata, source: string = "system"): Action {
    return {
      type: actionType,
      payload,
      timestamp: new Date(),
      source
    };
  }

  // ========== STATE QUERY FUNCTIONS (Pure) ==========

  getStatus(state: AgentState): string {
    return state.status;
  }

  getProgress(state: AgentState): number {
    return state.progress;
  }

  hasErrors(state: AgentState): boolean {
    return state.errors.length > 0;
  }

  getLatestMemory(state: AgentState, memoryType?: string): any | null {
    const filteredMemories = state.memories.filter(
      m => !memoryType || m.type === memoryType
    );
    return filteredMemories.length > 0 ? filteredMemories[filteredMemories.length - 1] : null;
  }

  getResult(state: AgentState, key: string): any {
    return state.results[key];
  }

  isComplete(state: AgentState): boolean {
    return ['completed', 'success'].includes(state.status) && state.progress >= 1.0;
  }

  isFailed(state: AgentState): boolean {
    return state.status === 'error' || state.errors.length > 0;
  }

  // ========== STATE TRANSFORMATION FUNCTIONS (Pure) ==========

  setStatus(state: AgentState, status: string, source: string = "system"): AgentState {
    const action = this.createAction(ActionType.SET_STATUS, { status }, source);
    return this.applyAction(state, action);
  }

  updateProgress(state: AgentState, progress: number, source: string = "system"): AgentState {
    const action = this.createAction(ActionType.UPDATE_PROGRESS, { progress }, source);
    return this.applyAction(state, action);
  }

  addMemory(
    state: AgentState,
    content: string,
    memoryType: string = "general",
    metadata: BaseMetadata = {},
    source: string = "system"
  ): AgentState {
    const action = this.createAction(
      ActionType.ADD_MEMORY,
      { content, memory_type: memoryType, metadata },
      source
    );
    return this.applyAction(state, action);
  }

  addResult(state: AgentState, key: string, value: any, source: string = "system"): AgentState {
    const action = this.createAction(ActionType.ADD_RESULT, { key, value }, source);
    return this.applyAction(state, action);
  }

  updateContext(
    state: AgentState,
    contextUpdates: BaseMetadata,
    source: string = "system"
  ): AgentState {
    const action = this.createAction(ActionType.UPDATE_CONTEXT, { context_updates: contextUpdates }, source);
    return this.applyAction(state, action);
  }

  setError(state: AgentState, error: string, source: string = "system"): AgentState {
    const action = this.createAction(ActionType.SET_ERROR, { error }, source);
    return this.applyAction(state, action);
  }

  clearErrors(state: AgentState, source: string = "system"): AgentState {
    const action = this.createAction(ActionType.CLEAR_ERROR, {}, source);
    return this.applyAction(state, action);
  }

  // ========== WORKFLOW ORCHESTRATION (Functional Style) ==========

  /**
   * Process a workflow step using functional patterns
   * Returns tuple of [new_state, step_result]
   */
  async processWorkflowStep(
    state: AgentState,
    stepName: string,
    stepFunction: WorkflowStepFunction,
    stepInput: BaseMetadata
  ): Promise<[AgentState, any]> {
    try {
      // Set status to running step
      state = this.setStatus(state, `running_${stepName}`);

      // Execute step function (can have side effects)
      const stepResult = await stepFunction(stepInput);

      // Update state with step result
      state = this.addResult(state, stepName, stepResult);
      state = this.addMemory(state, `Completed step: ${stepName}`, "workflow_step");

      // Update progress (assume each step is equal weight)
      const currentProgress = state.progress;
      const newProgress = Math.min(1.0, currentProgress + 0.1); // Increment by 10%
      state = this.updateProgress(state, newProgress);

      return [state, stepResult];

    } catch (error) {
      // Handle error functionally
      const errorMessage = `Step ${stepName} failed: ${String(error)}`;
      state = this.setError(state, errorMessage);
      return [state, null];
    }
  }

  /**
   * Compose multiple workflow steps into a single function
   * Functional composition pattern
   */
  composeWorkflowSteps(...stepFunctions: WorkflowStepFunction[]): 
    (initialState: AgentState, workflowInput: BaseMetadata) => Promise<AgentState> {
    
    return async (initialState: AgentState, workflowInput: BaseMetadata): Promise<AgentState> => {
      let currentState = initialState;
      let currentInput = workflowInput;

      for (let i = 0; i < stepFunctions.length; i++) {
        const stepFunction = stepFunctions[i];
        const stepName = `step_${i + 1}`;
        
        const [newState, stepResult] = await this.processWorkflowStep(
          currentState, stepName, stepFunction, currentInput
        );
        currentState = newState;

        // If step failed, stop execution
        if (this.hasErrors(currentState)) {
          break;
        }

        // Pass step result as input to next step
        currentInput = { previous_result: stepResult, ...currentInput };
      }

      // Set final status
      if (this.hasErrors(currentState)) {
        currentState = this.setStatus(currentState, "failed");
      } else {
        currentState = this.setStatus(currentState, "completed");
        currentState = this.updateProgress(currentState, 1.0);
      }

      return currentState;
    };
  }

  // ========== STATE SERIALIZATION (Pure) ==========

  /**
   * Serialize state to object - pure function
   */
  serializeState(state: AgentState): BaseMetadata {
    return {
      ...state,
      last_updated: state.last_updated.toISOString()
    };
  }

  /**
   * Deserialize state from object - pure function
   */
  deserializeState(stateData: BaseMetadata): AgentState {
    return {
      ...stateData,
      last_updated: new Date(stateData.last_updated)
    } as AgentState;
  }

  // ========== HIGHER-ORDER FUNCTIONS ==========

  /**
   * Apply transformation function to collection of states - pure function
   */
  mapOverStateCollection(
    states: AgentState[],
    transformFunc: (state: AgentState) => AgentState
  ): AgentState[] {
    return states.map(transformFunc);
  }

  /**
   * Filter states by predicate - pure function
   */
  filterStates(
    states: AgentState[],
    predicate: (state: AgentState) => boolean
  ): AgentState[] {
    return states.filter(predicate);
  }

  /**
   * Reduce collection of states to single state - pure function
   */
  reduceStates(
    states: AgentState[],
    reducerFunc: (acc: AgentState, current: AgentState) => AgentState
  ): AgentState | null {
    if (states.length === 0) return null;
    return states.slice(1).reduce(reducerFunc, states[0]);
  }

  // ========== VALIDATION FUNCTIONS (Pure) ==========

  /**
   * Validate that state transition is valid - pure function
   */
  validateStateTransition(fromState: AgentState, toState: AgentState): boolean {
    // Basic validation rules
    if (toState.last_updated < fromState.last_updated) {
      return false;
    }

    if (toState.progress < fromState.progress) {
      return false;
    }

    // Status transition validation
    const validTransitions: Record<string, string[]> = {
      'initialized': ['running', 'error'],
      'running': ['paused', 'completed', 'error', 'running'],
      'paused': ['running', 'error'],
      'completed': ['completed'],
      'error': ['running', 'error']
    };

    const fromStatus = fromState.status.split('_')[0]; // Handle "running_step1" -> "running"
    const toStatus = toState.status.split('_')[0];

    return (validTransitions[fromStatus] || []).includes(toStatus);
  }

  /**
   * Validate action is well-formed - pure function
   */
  validateAction(action: Action): boolean {
    if (!Object.values(ActionType).includes(action.type)) {
      return false;
    }

    if (typeof action.payload !== 'object' || action.payload === null) {
      return false;
    }

    if (!(action.timestamp instanceof Date)) {
      return false;
    }

    return true;
  }

  getServiceCapabilities(): ServiceCapabilities {
    return {
      service_id: this.serviceId,
      service_type: "stateless_reducer",
      capabilities: [
        "immutable_state_management",
        "pure_function_operations",
        "action_based_state_transitions",
        "functional_composition",
        "state_serialization",
        "workflow_orchestration"
      ],
      description: "Stateless reducer service implementing functional programming patterns",
      version: "1.0.0",
      factor_compliance: ["Factor 12: Make Your Agent a Stateless Reducer"],
      guarantees: [
        "No side effects in pure functions",
        "Immutable state objects",
        "Predictable state transitions",
        "Composable operations",
        "Testable components"
      ]
    };
  }
}
