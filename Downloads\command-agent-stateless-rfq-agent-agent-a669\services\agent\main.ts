/**
 * Main Application Entry Point
 * Converted from Python FastAPI to TypeScript Express/Fastify
 * 12-Factor Agent Service Implementation
 */

import express, { Express, Request, Response } from 'express';
import { FastifyInstance } from 'fastify';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { Logger } from 'winston';

// Import our modules
import { AgentConfig } from './core/config';
import { DatabaseService } from './core/database';
import { RedisService } from './core/redis_client';
import { MonitoringService } from './core/monitoring';
import { ErrorHandlerMiddleware, GlobalErrorHandler } from './middleware/error_handler';
import { createRateLimiter } from './middleware/rate_limiter';
import { createInputGatewayRoutes } from './api/input_gateway';
import { RFQWorkflowAgent } from './agents/rfq_agent';
import { MultiAgentOrchestrator } from './agents/multi_agent_orchestrator';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

// ========== APPLICATION CLASS ==========

/**
 * Main Agent Application
 */
export class AgentApplication {
  private app: Express;
  private config: AgentConfig;
  private database: DatabaseService;
  private redis: RedisService;
  private monitoring: MonitoringService;
  private rfqAgent: RFQWorkflowAgent;
  private orchestrator: MultiAgentOrchestrator;

  constructor() {
    this.app = express();
    this.config = new AgentConfig();
    this.database = new DatabaseService(this.config);
    this.redis = new RedisService(this.config);
    this.monitoring = new MonitoringService();
    this.rfqAgent = new RFQWorkflowAgent();
    this.orchestrator = new MultiAgentOrchestrator();
  }

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    logger.info('Initializing Agent Application');

    try {
      // Setup global error handlers
      GlobalErrorHandler.setup();

      // Initialize core services
      await this.database.initialize();
      await this.redis.initialize();
      await this.monitoring.initialize();

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      this.setupRoutes();

      // Setup error handling
      this.setupErrorHandling();

      logger.info('Agent Application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application', { error });
      throw error;
    }
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true
    }));

    // Performance middleware
    this.app.use(compression());

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Rate limiting
    const rateLimiter = createRateLimiter(100, this.redis); // 100 requests per minute
    this.app.use('/api', rateLimiter.middleware());

    // Request logging
    this.app.use((req: Request, res: Response, next) => {
      logger.info('Incoming request', {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  /**
   * Setup application routes
   */
  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', async (req: Request, res: Response) => {
      try {
        const healthStatus = await this.getHealthStatus();
        res.json(healthStatus);
      } catch (error) {
        res.status(500).json({ status: 'unhealthy', error: String(error) });
      }
    });

    // Metrics endpoint
    this.app.get('/metrics', async (req: Request, res: Response) => {
      try {
        const metrics = await this.monitoring.getMetrics();
        res.set('Content-Type', 'text/plain');
        res.send(metrics);
      } catch (error) {
        res.status(500).json({ error: String(error) });
      }
    });

    // Agent capabilities endpoint
    this.app.get('/api/capabilities', (req: Request, res: Response) => {
      res.json({
        rfq_agent: this.rfqAgent.getAgentCapabilities(),
        orchestrator: this.orchestrator.getOrchestratorCapabilities(),
        application: {
          name: 'Agent Service',
          version: '1.0.0',
          description: '12-Factor Agent Service for RFQ Processing',
          factor_compliance: [
            'Factor 1: Natural Language to Tool Calls',
            'Factor 2: Stateful Workflows',
            'Factor 3: Human-in-the-Loop',
            'Factor 4: Tools Are Just Structured Outputs',
            'Factor 10: Small, Focused Agents',
            'Factor 11: Trigger from Anywhere'
          ]
        }
      });
    });

    // RFQ workflow endpoints
    this.app.post('/api/rfq/execute', async (req: Request, res: Response) => {
      try {
        const { input_request, user_id, execution_id } = req.body;

        if (!input_request || !user_id) {
          return res.status(400).json({
            error: 'input_request and user_id are required'
          });
        }

        const result = await this.rfqAgent.executeRfqWorkflow(
          input_request,
          user_id,
          execution_id
        );

        res.json(result);
      } catch (error) {
        logger.error('RFQ workflow execution failed', { error });
        res.status(500).json({ error: String(error) });
      }
    });

    this.app.get('/api/rfq/status/:execution_id', async (req: Request, res: Response) => {
      try {
        const { execution_id } = req.params;
        const status = await this.rfqAgent.getWorkflowStatus(execution_id);
        res.json(status);
      } catch (error) {
        logger.error('Failed to get workflow status', { error });
        res.status(500).json({ error: String(error) });
      }
    });

    // Multi-agent orchestrator endpoint
    this.app.post('/api/orchestrator/process', async (req: Request, res: Response) => {
      try {
        const {
          rfq_request,
          region,
          category,
          budget_range,
          urgency,
          department
        } = req.body;

        if (!rfq_request || !region || !category) {
          return res.status(400).json({
            error: 'rfq_request, region, and category are required'
          });
        }

        const result = await this.orchestrator.processRfqRequest(
          rfq_request,
          region,
          category,
          budget_range,
          urgency,
          department
        );

        res.json(result);
      } catch (error) {
        logger.error('Multi-agent orchestration failed', { error });
        res.status(500).json({ error: String(error) });
      }
    });

    // Setup input gateway routes
    createInputGatewayRoutes(this.app);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        name: 'Agent Service',
        version: '1.0.0',
        description: '12-Factor Agent Service for RFQ Processing',
        status: 'running',
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * Setup error handling middleware
   */
  private setupErrorHandling(): void {
    // 404 handler
    this.app.use(ErrorHandlerMiddleware.notFound());

    // Global error handler
    this.app.use(ErrorHandlerMiddleware.handle());
  }

  /**
   * Get application health status
   */
  private async getHealthStatus(): Promise<Record<string, any>> {
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        database: 'unknown',
        redis: 'unknown',
        monitoring: 'unknown'
      },
      agents: {
        rfq_agent: 'healthy',
        orchestrator: 'healthy'
      }
    };

    try {
      // Check database health
      const dbHealth = await this.database.healthCheck();
      status.services.database = dbHealth.healthy ? 'healthy' : 'unhealthy';
    } catch (error) {
      status.services.database = 'unhealthy';
    }

    try {
      // Check Redis health
      const redisHealth = await this.redis.healthCheck();
      status.services.redis = redisHealth.healthy ? 'healthy' : 'unhealthy';
    } catch (error) {
      status.services.redis = 'unhealthy';
    }

    try {
      // Check monitoring health
      const monitoringHealth = await this.monitoring.healthCheck();
      status.services.monitoring = monitoringHealth.healthy ? 'healthy' : 'unhealthy';
    } catch (error) {
      status.services.monitoring = 'unhealthy';
    }

    // Overall status
    const allHealthy = Object.values(status.services).every(s => s === 'healthy');
    status.status = allHealthy ? 'healthy' : 'degraded';

    return status;
  }

  /**
   * Start the application server
   */
  async start(port: number = 8000): Promise<void> {
    try {
      await this.initialize();

      this.app.listen(port, () => {
        logger.info(`Agent Service started successfully`, {
          port,
          environment: process.env.NODE_ENV || 'development',
          pid: process.pid
        });
      });

    } catch (error) {
      logger.error('Failed to start Agent Service', { error });
      process.exit(1);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down Agent Service');

    try {
      // Close database connections
      await this.database.close();

      // Close Redis connections
      await this.redis.close();

      // Stop monitoring
      await this.monitoring.stop();

      logger.info('Agent Service shutdown complete');
    } catch (error) {
      logger.error('Error during shutdown', { error });
    }
  }

  /**
   * Get Express app instance
   */
  getApp(): Express {
    return this.app;
  }
}

// ========== APPLICATION STARTUP ==========

/**
 * Start the application if this file is run directly
 */
async function main(): Promise<void> {
  const app = new AgentApplication();
  const port = parseInt(process.env.PORT || '8000');

  // Handle graceful shutdown
  process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    await app.shutdown();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    await app.shutdown();
    process.exit(0);
  });

  // Start the application
  await app.start(port);
}

// Run if this is the main module
if (require.main === module) {
  main().catch((error) => {
    logger.error('Failed to start application', { error });
    process.exit(1);
  });
}

// Export for testing and external use
export default AgentApplication;