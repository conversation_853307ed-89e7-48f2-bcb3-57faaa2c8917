/**
 * Factor 10: Small, Focused Agents - Market Research Agent
 * Specialized agent focused solely on market intelligence and pricing research
 * Converted from Python to TypeScript
 */

import { Logger } from 'winston';
import { IsString, IsOptional, IsArray, IsNumber, IsEnum, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { StateGraph, CompiledStateGraph } from './rfq_agent';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

// ========== DATA CLASSES ==========

export class MarketResearchState {
  @IsString()
  query!: string;

  @IsString()
  region!: string;

  @IsString()
  category!: string;

  @IsOptional()
  @IsString()
  budget_range?: string;

  @IsString()
  urgency: string = 'medium';

  // Research results
  @IsOptional()
  price_analysis?: Record<string, any>;

  @IsOptional()
  market_trends?: Record<string, any>;

  @IsOptional()
  supplier_landscape?: Record<string, any>;

  @IsOptional()
  risk_assessment?: Record<string, any>;

  @IsOptional()
  regulatory_requirements?: Record<string, any>;

  // Metadata
  @IsNumber()
  @Min(0)
  @Max(1)
  research_quality_score: number = 0.0;

  @IsString()
  confidence_level: string = 'low';

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  data_sources?: string[] = [];

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : new Date())
  last_updated?: Date = new Date();
}

// ========== MOCK SERVICES ==========

interface PerplexityService {
  research_with_sources(query: string, context: string): Promise<{ content: string; sources: string[]; confidence?: number }>;
  generate_response(prompt: string, context: string): Promise<string>;
}

interface MemoryService {
  search_memories(query: string, agent_id: string, limit: number): Promise<any[]>;
  store_conversation_memory(agent_id: string, content: string, memory_type: string, metadata: Record<string, any>): Promise<void>;
}

interface ErrorManager {
  compact_error_for_context(error: any, context: Record<string, any>, execution_id: string, step_name: string): Promise<string>;
}

// Mock implementations
const mockPerplexityService: PerplexityService = {
  async research_with_sources(query: string, context: string) {
    return {
      content: `Mock research results for: ${query}`,
      sources: ['source1.com', 'source2.com'],
      confidence: 0.85
    };
  },
  async generate_response(prompt: string, context: string) {
    return `Mock response for: ${prompt.substring(0, 100)}...`;
  }
};

const mockMemoryService: MemoryService = {
  async search_memories(query: string, agent_id: string, limit: number) {
    return [{ content: 'Mock historical data', metadata: { relevance: 0.8 } }];
  },
  async store_conversation_memory(agent_id: string, content: string, memory_type: string, metadata: Record<string, any>) {
    logger.info('Storing memory', { agent_id, memory_type, content: content.substring(0, 100), metadata });
  }
};

const mockErrorManager: ErrorManager = {
  async compact_error_for_context(error: any, context: Record<string, any>, execution_id: string, step_name: string) {
    return `Error in ${step_name}: ${String(error)}`;
  }
};

// ========== MARKET RESEARCH AGENT ==========

export class MarketResearchAgent {
  private perplexity: PerplexityService;
  private memory: MemoryService;
  private errorManager: ErrorManager;
  private agentId = 'market_research_agent';
  private capabilities = [
    'price_analysis',
    'market_trends',
    'supplier_research',
    'risk_assessment',
    'regulatory_compliance'
  ];
  private workflow: CompiledStateGraph<MarketResearchState>;

  constructor() {
    this.perplexity = mockPerplexityService;
    this.memory = mockMemoryService;
    this.errorManager = mockErrorManager;
    this.workflow = this.buildWorkflow();
  }

  /**
   * Build specialized market research workflow
   */
  private buildWorkflow(): CompiledStateGraph<MarketResearchState> {
    const workflow = new StateGraph<MarketResearchState>();

    // Add nodes for each research capability
    workflow.addNode('price_analysis', this.analyzePrices.bind(this));
    workflow.addNode('market_trends', this.analyzeTrends.bind(this));
    workflow.addNode('supplier_research', this.researchSuppliers.bind(this));
    workflow.addNode('risk_assessment', this.assessRisks.bind(this));
    workflow.addNode('regulatory_check', this.checkRegulations.bind(this));
    workflow.addNode('consolidate_research', this.consolidateFindings.bind(this));

    // Define the research flow
    workflow.setEntryPoint('price_analysis');
    workflow.addEdge('price_analysis', 'market_trends');
    workflow.addEdge('market_trends', 'supplier_research');
    workflow.addEdge('supplier_research', 'risk_assessment');
    workflow.addEdge('risk_assessment', 'regulatory_check');
    workflow.addEdge('regulatory_check', 'consolidate_research');
    workflow.addEdge('consolidate_research', 'END');

    return workflow.compile();
  }

  // ========== WORKFLOW NODE IMPLEMENTATIONS ==========

  /**
   * Analyze current market prices for the requested items
   */
  private async analyzePrices(state: MarketResearchState): Promise<MarketResearchState> {
    try {
      // Search memory for historical pricing data
      const historicalData = await this.memory.search_memories(
        `pricing ${state.category} ${state.region}`,
        this.agentId,
        5
      );

      // Use Perplexity for current market price research
      const priceQuery = `
      Research current market prices for ${state.query} in ${state.region}.
      Include:
      1. Average price ranges by quality tier
      2. Recent price trends (last 6 months)
      3. Seasonal price variations
      4. Volume discount structures
      5. Key pricing factors and drivers

      Focus on actionable pricing intelligence for procurement decisions.
      `;

      const priceResearch = await this.perplexity.research_with_sources(
        priceQuery,
        'procurement_pricing'
      );

      // Analyze and structure pricing data
      const priceAnalysis = {
        current_price_range: this.extractPriceRange(priceResearch.content),
        price_trends: this.extractTrends(priceResearch.content),
        pricing_factors: this.extractPricingFactors(priceResearch.content),
        volume_discounts: this.extractVolumeInfo(priceResearch.content),
        market_position: this.assessMarketPosition(state.budget_range, priceResearch.content),
        sources: priceResearch.sources,
        confidence: priceResearch.confidence,
        timestamp: new Date().toISOString()
      };

      // Store findings in memory
      await this.memory.store_conversation_memory(
        this.agentId,
        `Price analysis for ${state.query}: ${JSON.stringify(priceAnalysis)}`,
        'market_intelligence',
        { category: state.category, region: state.region }
      );

      state.price_analysis = priceAnalysis;
      state.data_sources = [...(state.data_sources || []), ...priceResearch.sources];

      logger.info(`Completed price analysis for ${state.query}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: { query: state.query, region: state.region, category: state.category } },
        `market_research_${Date.now()}`,
        'price_analysis'
      );

      // Fallback to basic price estimation
      state.price_analysis = {
        current_price_range: 'Data unavailable - manual research required',
        error: errorContext,
        fallback_used: true
      };
      logger.error(`Price analysis failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Analyze market trends and future outlook
   */
  private async analyzeTrends(state: MarketResearchState): Promise<MarketResearchState> {
    try {
      const trendsQuery = `
      Analyze market trends for ${state.category} industry in ${state.region}.
      Include:
      1. Current market growth rate and outlook
      2. Technology disruptions affecting the market
      3. Supply chain trends and challenges
      4. Demand patterns and seasonality
      5. Competitive landscape changes
      6. Future price predictions (6-12 months)

      Provide actionable insights for procurement timing decisions.
      `;

      const trendsResearch = await this.perplexity.research_with_sources(
        trendsQuery,
        'market_trends'
      );

      const marketTrends = {
        growth_outlook: this.extractGrowthData(trendsResearch.content),
        technology_impact: this.extractTechTrends(trendsResearch.content),
        supply_chain_status: this.extractSupplyChainInfo(trendsResearch.content),
        demand_patterns: this.extractDemandPatterns(trendsResearch.content),
        competitive_dynamics: this.extractCompetitionInfo(trendsResearch.content),
        procurement_timing: this.recommendTiming(trendsResearch.content, state.urgency),
        sources: trendsResearch.sources,
        confidence: trendsResearch.confidence,
        timestamp: new Date().toISOString()
      };

      await this.memory.store_conversation_memory(
        this.agentId,
        `Market trends for ${state.category}: ${JSON.stringify(marketTrends)}`,
        'market_intelligence',
        { category: state.category, region: state.region }
      );

      state.market_trends = marketTrends;
      state.data_sources = [...(state.data_sources || []), ...trendsResearch.sources];

      logger.info(`Completed trends analysis for ${state.category}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: { query: state.query, region: state.region, category: state.category } },
        `market_research_${Date.now()}`,
        'trends_analysis'
      );

      state.market_trends = {
        error: errorContext,
        fallback_used: true
      };
      logger.error(`Trends analysis failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Research supplier landscape and vendor ecosystem
   */
  private async researchSuppliers(state: MarketResearchState): Promise<MarketResearchState> {
    try {
      const supplierQuery = `
      Research suppliers and vendors for ${state.query} in ${state.region}.
      Include:
      1. Major suppliers and market leaders
      2. Regional/local supplier options
      3. Supplier capabilities and specializations
      4. Supplier reliability and reputation ratings
      5. New entrants and emerging suppliers
      6. Supplier risk factors and financial stability

      Focus on actionable supplier intelligence for vendor selection.
      `;

      const supplierResearch = await this.perplexity.research_with_sources(
        supplierQuery,
        'supplier_intelligence'
      );

      const supplierLandscape = {
        major_suppliers: this.extractMajorSuppliers(supplierResearch.content),
        regional_options: this.extractRegionalSuppliers(supplierResearch.content),
        supplier_capabilities: this.extractCapabilities(supplierResearch.content),
        reputation_analysis: this.extractReputationData(supplierResearch.content),
        emerging_suppliers: this.extractNewEntrants(supplierResearch.content),
        risk_factors: this.extractSupplierRisks(supplierResearch.content),
        recommendation_score: this.scoreSupplierLandscape(supplierResearch.content),
        sources: supplierResearch.sources,
        confidence: supplierResearch.confidence,
        timestamp: new Date().toISOString()
      };

      await this.memory.store_conversation_memory(
        this.agentId,
        `Supplier landscape for ${state.query}: ${JSON.stringify(supplierLandscape)}`,
        'supplier_intelligence',
        { category: state.category, region: state.region }
      );

      state.supplier_landscape = supplierLandscape;
      state.data_sources = [...(state.data_sources || []), ...supplierResearch.sources];

      logger.info(`Completed supplier research for ${state.query}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: { query: state.query, region: state.region, category: state.category } },
        `market_research_${Date.now()}`,
        'supplier_research'
      );

      state.supplier_landscape = {
        error: errorContext,
        fallback_used: true
      };
      logger.error(`Supplier research failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Assess market and procurement risks
   */
  private async assessRisks(state: MarketResearchState): Promise<MarketResearchState> {
    try {
      const riskQuery = `
      Assess procurement risks for ${state.query} in ${state.region}.
      Include:
      1. Supply chain risk factors
      2. Price volatility and market risks
      3. Regulatory and compliance risks
      4. Quality and performance risks
      5. Geopolitical and economic risks
      6. Risk mitigation strategies

      Provide risk ratings and mitigation recommendations.
      `;

      const riskResearch = await this.perplexity.research_with_sources(
        riskQuery,
        'risk_assessment'
      );

      const riskAssessment = {
        supply_chain_risks: this.extractSupplyRisks(riskResearch.content),
        price_volatility: this.extractPriceRisks(riskResearch.content),
        regulatory_risks: this.extractRegulatoryRisks(riskResearch.content),
        quality_risks: this.extractQualityRisks(riskResearch.content),
        external_risks: this.extractExternalRisks(riskResearch.content),
        risk_score: this.calculateOverallRiskScore(riskResearch.content),
        mitigation_strategies: this.extractMitigationStrategies(riskResearch.content),
        sources: riskResearch.sources,
        confidence: riskResearch.confidence,
        timestamp: new Date().toISOString()
      };

      await this.memory.store_conversation_memory(
        this.agentId,
        `Risk assessment for ${state.query}: ${JSON.stringify(riskAssessment)}`,
        'risk_intelligence',
        { category: state.category, region: state.region }
      );

      state.risk_assessment = riskAssessment;
      state.data_sources = [...(state.data_sources || []), ...riskResearch.sources];

      logger.info(`Completed risk assessment for ${state.query}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: { query: state.query, region: state.region, category: state.category } },
        `market_research_${Date.now()}`,
        'risk_assessment'
      );

      state.risk_assessment = {
        error: errorContext,
        fallback_used: true
      };
      logger.error(`Risk assessment failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Check regulatory requirements and compliance
   */
  private async checkRegulations(state: MarketResearchState): Promise<MarketResearchState> {
    try {
      const regulatoryQuery = `
      Research regulatory requirements for ${state.query} in ${state.region}.
      Include:
      1. Import/export regulations and tariffs
      2. Safety and quality standards
      3. Environmental compliance requirements
      4. Industry-specific regulations
      5. Certification and testing requirements
      6. Recent regulatory changes and updates

      Provide compliance checklist and requirements summary.
      `;

      const regulatoryResearch = await this.perplexity.research_with_sources(
        regulatoryQuery,
        'regulatory_compliance'
      );

      const regulatoryRequirements = {
        import_export: this.extractTradeRegulations(regulatoryResearch.content),
        safety_standards: this.extractSafetyRequirements(regulatoryResearch.content),
        environmental: this.extractEnvironmentalReqs(regulatoryResearch.content),
        industry_specific: this.extractIndustryRegs(regulatoryResearch.content),
        certifications: this.extractCertificationReqs(regulatoryResearch.content),
        compliance_score: this.assessComplianceComplexity(regulatoryResearch.content),
        recent_changes: this.extractRecentChanges(regulatoryResearch.content),
        sources: regulatoryResearch.sources,
        confidence: regulatoryResearch.confidence,
        timestamp: new Date().toISOString()
      };

      await this.memory.store_conversation_memory(
        this.agentId,
        `Regulatory requirements for ${state.query}: ${JSON.stringify(regulatoryRequirements)}`,
        'regulatory_intelligence',
        { category: state.category, region: state.region }
      );

      state.regulatory_requirements = regulatoryRequirements;
      state.data_sources = [...(state.data_sources || []), ...regulatoryResearch.sources];

      logger.info(`Completed regulatory check for ${state.query}`);
      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: { query: state.query, region: state.region, category: state.category } },
        `market_research_${Date.now()}`,
        'regulatory_check'
      );

      state.regulatory_requirements = {
        error: errorContext,
        fallback_used: true
      };
      logger.error(`Regulatory check failed: ${errorContext}`);
      return state;
    }
  }

  /**
   * Consolidate all research findings and generate final intelligence
   */
  private async consolidateFindings(state: MarketResearchState): Promise<MarketResearchState> {
    try {
      // Calculate overall research quality score
      const qualityFactors = [
        state.price_analysis && !state.price_analysis.fallback_used ? 0.25 : 0.0,
        state.market_trends && !state.market_trends.fallback_used ? 0.20 : 0.0,
        state.supplier_landscape && !state.supplier_landscape.fallback_used ? 0.20 : 0.0,
        state.risk_assessment && !state.risk_assessment.fallback_used ? 0.20 : 0.0,
        state.regulatory_requirements && !state.regulatory_requirements.fallback_used ? 0.15 : 0.0
      ];

      state.research_quality_score = qualityFactors.reduce((sum, factor) => sum + factor, 0);

      // Determine confidence level
      if (state.research_quality_score >= 0.8) {
        state.confidence_level = 'high';
      } else if (state.research_quality_score >= 0.6) {
        state.confidence_level = 'medium';
      } else if (state.research_quality_score >= 0.4) {
        state.confidence_level = 'low';
      } else {
        state.confidence_level = 'very_low';
      }

      // Update timestamp
      state.last_updated = new Date();

      // Store consolidated research
      await this.memory.store_conversation_memory(
        this.agentId,
        `Consolidated market research for ${state.query}: Quality=${state.research_quality_score}, Confidence=${state.confidence_level}`,
        'consolidated_research',
        {
          category: state.category,
          region: state.region,
          quality_score: state.research_quality_score,
          confidence: state.confidence_level
        }
      );

      logger.info(`Consolidated research findings`, {
        query: state.query,
        quality_score: state.research_quality_score,
        confidence: state.confidence_level,
        sources_count: state.data_sources?.length || 0
      });

      return state;

    } catch (error) {
      const errorContext = await this.errorManager.compact_error_for_context(
        error,
        { state: { query: state.query, region: state.region, category: state.category } },
        `market_research_${Date.now()}`,
        'consolidate_findings'
      );

      logger.error(`Research consolidation failed: ${errorContext}`);
      state.research_quality_score = 0.1;
      state.confidence_level = 'very_low';
      return state;
    }
  }

  // ========== HELPER METHODS ==========

  /**
   * Extract price range information from research content
   */
  private extractPriceRange(content: string): string {
    // Mock implementation - in real scenario would parse content
    return '$1,000 - $5,000 per unit (estimated based on market research)';
  }

  private extractTrends(content: string): Record<string, any> {
    return {
      direction: 'stable_to_increasing',
      rate: '3-5% annually',
      factors: ['material costs', 'demand growth', 'supply constraints']
    };
  }

  private extractPricingFactors(content: string): string[] {
    return ['Raw material costs', 'Manufacturing complexity', 'Market demand', 'Competition level'];
  }

  private extractVolumeInfo(content: string): Record<string, any> {
    return {
      available: true,
      tiers: [
        { quantity: '1-10 units', discount: '0%' },
        { quantity: '11-50 units', discount: '5-10%' },
        { quantity: '50+ units', discount: '10-15%' }
      ]
    };
  }

  private assessMarketPosition(budgetRange: string | undefined, content: string): string {
    if (!budgetRange) return 'Budget range not specified';
    return 'Competitive positioning within specified budget range';
  }

  private extractGrowthData(content: string): Record<string, any> {
    return {
      current_rate: '5-8% annually',
      outlook: 'positive',
      drivers: ['infrastructure investment', 'technology adoption']
    };
  }

  private extractTechTrends(content: string): Record<string, any> {
    return {
      emerging_technologies: ['IoT integration', 'Smart systems', 'Sustainability features'],
      disruption_level: 'moderate',
      adoption_timeline: '2-5 years'
    };
  }

  private extractSupplyChainInfo(content: string): Record<string, any> {
    return {
      status: 'stable with some constraints',
      challenges: ['material availability', 'logistics costs'],
      outlook: 'improving'
    };
  }

  private extractDemandPatterns(content: string): Record<string, any> {
    return {
      seasonality: 'moderate seasonal variation',
      peak_periods: ['Q2', 'Q4'],
      growth_segments: ['commercial', 'industrial']
    };
  }

  private extractCompetitionInfo(content: string): Record<string, any> {
    return {
      intensity: 'moderate to high',
      key_players: ['Market Leader A', 'Regional Player B', 'Emerging Company C'],
      differentiation: ['quality', 'service', 'innovation']
    };
  }

  private recommendTiming(content: string, urgency: string): string {
    if (urgency === 'high') {
      return 'Immediate procurement recommended despite potential premium pricing';
    } else if (urgency === 'low') {
      return 'Consider waiting 3-6 months for better pricing opportunities';
    }
    return 'Current timing is reasonable for procurement';
  }

  // Additional helper methods for supplier research
  private extractMajorSuppliers(content: string): string[] {
    return ['Global Supplier Corp', 'Industry Leader Inc', 'Market Dominant Ltd'];
  }

  private extractRegionalSuppliers(content: string): string[] {
    return ['Regional Specialist A', 'Local Provider B', 'Area Expert C'];
  }

  private extractCapabilities(content: string): Record<string, any> {
    return {
      manufacturing: ['Standard production', 'Custom fabrication', 'Quality assurance'],
      services: ['Design support', 'Installation', 'Maintenance'],
      certifications: ['ISO 9001', 'Industry standards', 'Quality certifications']
    };
  }

  private extractReputationData(content: string): Record<string, any> {
    return {
      overall_rating: '4.2/5.0',
      customer_satisfaction: '85%',
      reliability_score: 'High',
      common_feedback: ['Good quality', 'Reliable delivery', 'Responsive support']
    };
  }

  private extractNewEntrants(content: string): string[] {
    return ['Innovative Startup X', 'Tech-Forward Company Y', 'Disruptive Player Z'];
  }

  private extractSupplierRisks(content: string): Record<string, any> {
    return {
      financial_risks: ['Market concentration', 'Economic sensitivity'],
      operational_risks: ['Capacity constraints', 'Quality variations'],
      strategic_risks: ['Technology changes', 'Competitive pressure']
    };
  }

  private scoreSupplierLandscape(content: string): number {
    return 0.75; // Mock score indicating good supplier availability
  }

  // Risk assessment helper methods
  private extractSupplyRisks(content: string): Record<string, any> {
    return {
      level: 'medium',
      factors: ['Single source dependencies', 'Geographic concentration', 'Material availability'],
      mitigation: ['Supplier diversification', 'Strategic inventory', 'Alternative sourcing']
    };
  }

  private extractPriceRisks(content: string): Record<string, any> {
    return {
      volatility: 'moderate',
      factors: ['Commodity prices', 'Currency fluctuation', 'Market demand'],
      hedging_options: ['Fixed-price contracts', 'Volume commitments', 'Price escalation clauses']
    };
  }

  private extractRegulatoryRisks(content: string): Record<string, any> {
    return {
      level: 'low to medium',
      areas: ['Safety standards', 'Environmental regulations', 'Trade policies'],
      monitoring: ['Regulatory updates', 'Compliance audits', 'Industry associations']
    };
  }

  private extractQualityRisks(content: string): Record<string, any> {
    return {
      level: 'low',
      factors: ['Manufacturing variations', 'Material defects', 'Process control'],
      controls: ['Quality agreements', 'Inspection protocols', 'Supplier audits']
    };
  }

  private extractExternalRisks(content: string): Record<string, any> {
    return {
      geopolitical: 'stable',
      economic: 'moderate uncertainty',
      natural_disasters: 'low impact',
      cyber_security: 'standard precautions needed'
    };
  }

  private calculateOverallRiskScore(content: string): number {
    return 0.35; // Mock score indicating moderate-low risk
  }

  private extractMitigationStrategies(content: string): string[] {
    return [
      'Diversify supplier base across multiple regions',
      'Establish strategic inventory buffers',
      'Implement robust quality assurance programs',
      'Monitor market conditions and regulatory changes',
      'Develop contingency sourcing plans'
    ];
  }

  // Regulatory helper methods
  private extractTradeRegulations(content: string): Record<string, any> {
    return {
      tariffs: '5-15% depending on origin',
      import_licenses: 'Standard import documentation required',
      restrictions: 'No significant restrictions identified'
    };
  }

  private extractSafetyRequirements(content: string): Record<string, any> {
    return {
      standards: ['ANSI', 'OSHA', 'Industry-specific standards'],
      testing: 'Third-party testing may be required',
      documentation: 'Safety certificates and compliance documentation'
    };
  }

  private extractEnvironmentalReqs(content: string): Record<string, any> {
    return {
      regulations: ['EPA compliance', 'Local environmental standards'],
      certifications: ['Environmental management systems', 'Sustainability certifications'],
      reporting: 'Environmental impact reporting may be required'
    };
  }

  private extractIndustryRegs(content: string): Record<string, any> {
    return {
      specific_standards: ['Industry association standards', 'Professional certifications'],
      compliance_timeline: 'Ongoing compliance monitoring required',
      updates: 'Regular review of regulatory changes'
    };
  }

  private extractCertificationReqs(content: string): Record<string, any> {
    return {
      required: ['Quality certifications', 'Safety approvals'],
      optional: ['Performance certifications', 'Sustainability ratings'],
      timeline: '30-90 days for certification processes'
    };
  }

  private assessComplianceComplexity(content: string): number {
    return 0.6; // Mock score indicating moderate compliance complexity
  }

  private extractRecentChanges(content: string): Record<string, any> {
    return {
      recent_updates: ['Updated safety standards effective Q1 2024'],
      pending_changes: ['Proposed environmental regulations under review'],
      impact_assessment: 'Low to moderate impact on procurement processes'
    };
  }

  // ========== PUBLIC API ==========

  /**
   * Main entry point for market research
   */
  async conductMarketResearch(
    query: string,
    region: string,
    category: string,
    budgetRange?: string,
    urgency: string = 'medium'
  ): Promise<Record<string, any>> {
    try {
      const startTime = Date.now();

      // Initialize research state
      const state: MarketResearchState = {
        query,
        region,
        category,
        budget_range: budgetRange,
        urgency,
        research_quality_score: 0.0,
        confidence_level: 'low',
        data_sources: [],
        last_updated: new Date()
      };

      logger.info(`Starting market research for ${query} in ${region}`);

      // Execute research workflow
      const finalState = await this.workflow.invoke(state);

      // Calculate total execution time
      const totalTime = Date.now() - startTime;

      // Generate comprehensive response
      const response = {
        success: true,
        execution_time: totalTime,
        query: finalState.query,
        region: finalState.region,
        category: finalState.category,

        // Research results
        price_analysis: finalState.price_analysis,
        market_trends: finalState.market_trends,
        supplier_landscape: finalState.supplier_landscape,
        risk_assessment: finalState.risk_assessment,
        regulatory_requirements: finalState.regulatory_requirements,

        // Quality metrics
        research_quality_score: finalState.research_quality_score,
        confidence_level: finalState.confidence_level,
        data_sources: finalState.data_sources,
        last_updated: finalState.last_updated,

        // Metadata
        agent_id: this.agentId,
        timestamp: new Date().toISOString()
      };

      logger.info(`Market research completed successfully`, {
        execution_time: totalTime,
        quality_score: finalState.research_quality_score,
        confidence: finalState.confidence_level,
        sources_count: finalState.data_sources?.length || 0
      });

      return response;

    } catch (error) {
      logger.error('Market research failed', { error });

      return {
        success: false,
        error: String(error),
        agent_id: this.agentId,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get agent capabilities
   */
  getAgentCapabilities(): Record<string, any> {
    return {
      agent_id: this.agentId,
      agent_type: 'market_research',
      capabilities: this.capabilities,
      description: 'Specialized agent for market intelligence and pricing research',
      version: '1.0.0',
      factor_compliance: ['Factor 10: Small, Focused Agents'],
      research_areas: [
        'price_analysis',
        'market_trends',
        'supplier_intelligence',
        'risk_assessment',
        'regulatory_compliance'
      ],
      quality_metrics: {
        typical_quality_score: '0.6-0.9',
        confidence_levels: ['very_low', 'low', 'medium', 'high'],
        execution_time: '2-5 minutes',
        data_sources: 'Multiple verified sources'
      }
    };
  }
}

// Export default
export default MarketResearchAgent;