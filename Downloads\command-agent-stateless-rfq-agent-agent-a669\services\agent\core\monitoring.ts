/**
 * Comprehensive Monitoring Service
 * Implements metrics collection for all 12-Factor Agent components
 * Converted from Python Prometheus to TypeScript prom-client
 */

import {
  Counter,
  Histogram,
  Gauge,
  Summary,
  register,
  collectDefaultMetrics
} from 'prom-client';
import { Logger } from 'winston';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

/**
 * Centralized metrics collection for the AI Agent system
 * Tracks all 12-Factor Agent components with detailed metrics
 */
export class MetricsCollector {
  // ========== GENERAL SYSTEM METRICS ==========
  public readonly httpRequestsTotal: Counter<string>;
  public readonly httpRequestDuration: Histogram<string>;

  // ========== FACTOR 1: NATURAL LANGUAGE TO TOOL CALLS ==========
  public readonly toolCallsTotal: Counter<string>;
  public readonly toolCallDuration: Histogram<string>;
  public readonly perplexityApiCalls: Counter<string>;

  // ========== FACTOR 2: OWN YOUR PROMPTS ==========
  public readonly promptRetrievals: Counter<string>;
  public readonly promptCacheHits: Counter<string>;

  // ========== FACTOR 3: OWN YOUR CONTEXT WINDOW ==========
  public readonly memorySearches: Counter<string>;
  public readonly memorySearchDuration: Histogram<string>;
  public readonly memoryConsolidations: Counter<string>;

  // ========== FACTOR 4: UNIVERSAL INPUT HANDLER ==========
  public readonly inputRequests: Counter<string>;
  public readonly inputProcessingDuration: Histogram<string>;

  // ========== FACTOR 5: STATELESS REDUCER ==========
  public readonly stateReductions: Counter<string>;
  public readonly stateSize: Histogram<string>;

  // ========== FACTOR 6: VECTOR DATABASE ==========
  public readonly vectorOperations: Counter<string>;
  public readonly vectorSearchDuration: Histogram<string>;
  public readonly vectorIndexSize: Gauge<string>;

  // ========== FACTOR 7: HUMAN IN THE LOOP ==========
  public readonly humanTasksCreated: Counter<string>;
  public readonly humanTasksCompleted: Counter<string>;
  public readonly humanTaskDuration: Histogram<string>;
  public readonly humanTasksPending: Gauge<string>;

  // ========== FACTOR 8: OWN YOUR CONTROL FLOW ==========
  public readonly langgraphWorkflows: Counter<string>;
  public readonly langgraphWorkflowDuration: Histogram<string>;
  public readonly workflowSteps: Counter<string>;

  // ========== FACTOR 9: COMPACT ERRORS ==========
  public readonly errorCompactions: Counter<string>;
  public readonly errorPatternMatches: Counter<string>;
  public readonly errorContextSize: Histogram<string>;

  // ========== FACTOR 10: SMALL FOCUSED AGENTS ==========
  public readonly agentExecutions: Counter<string>;
  public readonly agentCoordination: Counter<string>;
  public readonly agentQualityScores: Histogram<string>;

  // ========== FACTOR 11: BUSINESS METRICS ==========
  public readonly businessOperations: Counter<string>;
  public readonly businessMetrics: Histogram<string>;

  // ========== FACTOR 12: DEPLOYMENT METRICS ==========
  public readonly activeWorkflows: Gauge<string>;
  public readonly queueDepth: Gauge<string>;

  constructor() {
    // Enable default metrics collection
    collectDefaultMetrics();

    // Initialize all metrics
    this.httpRequestsTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total HTTP requests',
      labelNames: ['method', 'endpoint', 'status']
    });

    this.httpRequestDuration = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration',
      labelNames: ['method', 'endpoint']
    });

    this.toolCallsTotal = new Counter({
      name: 'tool_calls_total',
      help: 'Total tool calls made',
      labelNames: ['tool_name', 'status']
    });

    this.toolCallDuration = new Histogram({
      name: 'tool_call_duration_seconds',
      help: 'Tool call execution time',
      labelNames: ['tool_name']
    });

    this.perplexityApiCalls = new Counter({
      name: 'perplexity_api_calls_total',
      help: 'Total Perplexity API calls',
      labelNames: ['operation_type', 'status']
    });

    this.promptRetrievals = new Counter({
      name: 'prompt_retrievals_total',
      help: 'Total prompt retrievals',
      labelNames: ['prompt_name', 'version']
    });

    this.promptCacheHits = new Counter({
      name: 'prompt_cache_hits_total',
      help: 'Prompt cache hits'
    });

    this.memorySearches = new Counter({
      name: 'memory_searches_total',
      help: 'Total memory searches',
      labelNames: ['search_type']
    });

    this.memorySearchDuration = new Histogram({
      name: 'memory_search_duration_seconds',
      help: 'Memory search duration',
      labelNames: ['search_type']
    });

    this.memoryConsolidations = new Counter({
      name: 'memory_consolidation_operations_total',
      help: 'Memory consolidation operations',
      labelNames: ['status']
    });

    this.inputRequests = new Counter({
      name: 'input_requests_total',
      help: 'Total input requests processed',
      labelNames: ['source', 'format', 'status']
    });

    this.inputProcessingDuration = new Histogram({
      name: 'input_processing_duration_seconds',
      help: 'Input processing duration',
      labelNames: ['source', 'format']
    });

    this.stateReductions = new Counter({
      name: 'state_reductions_total',
      help: 'State reduction operations',
      labelNames: ['action_type']
    });

    this.stateSize = new Histogram({
      name: 'state_size_bytes',
      help: 'State size after reduction'
    });

    this.vectorOperations = new Counter({
      name: 'vector_operations_total',
      help: 'Vector database operations',
      labelNames: ['operation_type', 'status']
    });

    this.vectorSearchDuration = new Histogram({
      name: 'vector_search_duration_seconds',
      help: 'Vector search duration',
      labelNames: ['search_type']
    });

    this.vectorIndexSize = new Gauge({
      name: 'vector_index_size_total',
      help: 'Total vectors in index'
    });

    this.humanTasksCreated = new Counter({
      name: 'human_tasks_created_total',
      help: 'Human tasks created',
      labelNames: ['task_type']
    });

    this.humanTasksCompleted = new Counter({
      name: 'human_tasks_completed_total',
      help: 'Human tasks completed',
      labelNames: ['task_type']
    });

    this.humanTaskDuration = new Histogram({
      name: 'human_task_duration_seconds',
      help: 'Human task completion time',
      labelNames: ['task_type']
    });

    this.humanTasksPending = new Gauge({
      name: 'human_tasks_pending_total',
      help: 'Currently pending human tasks'
    });

    this.langgraphWorkflows = new Counter({
      name: 'langgraph_workflow_executions_total',
      help: 'LangGraph workflow executions',
      labelNames: ['workflow_type', 'status']
    });

    this.langgraphWorkflowDuration = new Histogram({
      name: 'langgraph_workflow_duration_seconds',
      help: 'LangGraph workflow duration',
      labelNames: ['workflow_type']
    });

    this.workflowSteps = new Counter({
      name: 'workflow_steps_total',
      help: 'Workflow steps executed',
      labelNames: ['workflow_type', 'step_name', 'status']
    });

    this.errorCompactions = new Counter({
      name: 'error_manager_compactions_total',
      help: 'Error compactions performed'
    });

    this.errorPatternMatches = new Counter({
      name: 'error_manager_pattern_matches_total',
      help: 'Error pattern matches found'
    });

    this.errorContextSize = new Histogram({
      name: 'error_context_size_chars',
      help: 'Error context size after compaction'
    });

    this.agentExecutions = new Counter({
      name: 'agent_executions_total',
      help: 'Agent executions',
      labelNames: ['agent_type', 'status']
    });

    this.agentCoordination = new Counter({
      name: 'multi_agent_coordinations_total',
      help: 'Multi-agent coordinations',
      labelNames: ['coordination_type']
    });

    this.agentQualityScores = new Histogram({
      name: 'agent_quality_scores',
      help: 'Agent execution quality scores',
      labelNames: ['agent_type']
    });

    this.businessOperations = new Counter({
      name: 'business_operations_total',
      help: 'Business operations executed',
      labelNames: ['operation_type', 'status']
    });

    this.businessMetrics = new Histogram({
      name: 'business_metrics_value',
      help: 'Business metrics values',
      labelNames: ['metric_type']
    });

    this.activeWorkflows = new Gauge({
      name: 'active_workflows_total',
      help: 'Currently active workflows'
    });

    this.queueDepth = new Gauge({
      name: 'queue_depth_total',
      help: 'Queue depth by queue name',
      labelNames: ['queue_name']
    });
  }

  // ========== METRIC RECORDING METHODS ==========

  /**
   * Record HTTP request metrics
   */
  recordHttpRequest(method: string, endpoint: string, status: number, duration: number): void {
    this.httpRequestsTotal.labels(method, endpoint, status.toString()).inc();
    this.httpRequestDuration.labels(method, endpoint).observe(duration);
  }

  /**
   * Record tool call metrics
   */
  recordToolCall(toolName: string, status: string, duration: number): void {
    this.toolCallsTotal.labels(toolName, status).inc();
    this.toolCallDuration.labels(toolName).observe(duration);
  }

  /**
   * Record memory search metrics
   */
  recordMemorySearch(searchType: string, duration: number): void {
    this.memorySearches.labels(searchType).inc();
    this.memorySearchDuration.labels(searchType).observe(duration);
  }

  /**
   * Record workflow execution metrics
   */
  recordWorkflowExecution(workflowType: string, status: string, duration: number): void {
    this.langgraphWorkflows.labels(workflowType, status).inc();
    this.langgraphWorkflowDuration.labels(workflowType).observe(duration);
  }

  /**
   * Record agent execution metrics
   */
  recordAgentExecution(agentType: string, status: string, qualityScore: number): void {
    this.agentExecutions.labels(agentType, status).inc();
    this.agentQualityScores.labels(agentType).observe(qualityScore);
  }

  /**
   * Record input processing metrics
   */
  recordInputProcessing(source: string, format: string, status: string, duration: number): void {
    this.inputRequests.labels(source, format, status).inc();
    this.inputProcessingDuration.labels(source, format).observe(duration);
  }

  /**
   * Record human task metrics
   */
  recordHumanTask(taskType: string, status: string, duration?: number): void {
    if (status === 'created') {
      this.humanTasksCreated.labels(taskType).inc();
      this.humanTasksPending.inc();
    } else if (status === 'completed') {
      this.humanTasksCompleted.labels(taskType).inc();
      this.humanTasksPending.dec();
      if (duration !== undefined) {
        this.humanTaskDuration.labels(taskType).observe(duration);
      }
    }
  }

  /**
   * Record error compaction metrics
   */
  recordErrorCompaction(contextSize: number): void {
    this.errorCompactions.inc();
    this.errorContextSize.observe(contextSize);
  }

  /**
   * Record state reduction metrics
   */
  recordStateReduction(actionType: string): void {
    this.stateReductions.labels(actionType).inc();
  }

  /**
   * Update active workflows gauge
   */
  updateActiveWorkflows(count: number): void {
    this.activeWorkflows.set(count);
  }

  /**
   * Update queue depth gauge
   */
  updateQueueDepth(queueName: string, depth: number): void {
    this.queueDepth.labels(queueName).set(depth);
  }

  /**
   * Get all metrics in Prometheus format
   */
  async getMetrics(): Promise<string> {
    return register.metrics();
  }
}

// ========== MONITORING DECORATORS ==========

/**
 * Decorator to monitor execution time of functions
 */
export function monitorExecutionTime(metricName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();

      try {
        const result = await method.apply(this, args);
        const duration = (Date.now() - startTime) / 1000;

        // Record based on metric name
        const collector = metricsCollector;
        if (metricName in collector) {
          const metric = (collector as any)[metricName];
          if (metric && typeof metric.observe === 'function') {
            metric.observe(duration);
          }
        }

        return result;
      } catch (error) {
        const duration = (Date.now() - startTime) / 1000;
        logger.error(`Function ${propertyName} failed after ${duration.toFixed(2)}s: ${error}`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to count function calls
 */
export function countCalls(counterName: string, labels: string[] = []) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        const result = await method.apply(this, args);

        // Record counter increment
        const collector = metricsCollector;
        if (counterName in collector) {
          const counter = (collector as any)[counterName];
          if (counter && typeof counter.inc === 'function') {
            if (labels.length > 0) {
              counter.labels(...labels).inc();
            } else {
              counter.inc();
            }
          }
        }

        return result;
      } catch (error) {
        logger.error(`Function ${propertyName} failed: ${error}`);
        throw error;
      }
    };

    return descriptor;
  };
}

// ========== MONITORING SERVICE ==========

/**
 * Monitoring service for comprehensive system observability
 * Integrates with Prometheus and Grafana for visualization
 */
export class MonitoringService {
  private readonly serviceId = 'monitoring_service';

  constructor(public readonly collector: MetricsCollector) {}

  /**
   * Get overall system health metrics
   */
  getSystemHealth(): Record<string, any> {
    return {
      timestamp: new Date().toISOString(),
      service_status: 'healthy',
      metrics_collected: true,
      prometheus_endpoint: '/metrics',
      grafana_dashboard: '/grafana/d/ai-agent-overview',
      alert_rules_active: true
    };
  }

  /**
   * Get summary of all collected metrics
   */
  getMetricsSummary(): Record<string, string> {
    return {
      http_requests: 'Total HTTP requests processed',
      tool_calls: 'AI tool calls and executions',
      memory_operations: 'Memory search and consolidation operations',
      workflow_executions: 'LangGraph workflow executions',
      agent_coordinations: 'Multi-agent orchestrations',
      input_processing: 'Universal input handler operations',
      human_tasks: 'Human-in-the-loop task management',
      state_management: 'Stateless reducer operations',
      vector_database: 'PGVector operations and performance',
      business_metrics: 'RFQ workflow and business logic metrics'
    };
  }

  /**
   * Return monitoring service capabilities
   */
  getMonitoringCapabilities(): Record<string, any> {
    return {
      service_id: this.serviceId,
      monitoring_type: 'prometheus_grafana',
      capabilities: [
        'real_time_metrics_collection',
        'performance_monitoring',
        'error_tracking',
        'business_metrics',
        'alerting_rules',
        'dashboard_visualization'
      ],
      metrics_categories: [
        'system_health',
        'performance_metrics',
        'business_logic',
        'error_management',
        'resource_utilization'
      ],
      factor_coverage: 'All 12 factors monitored',
      description: 'Comprehensive monitoring for LangGraph AI Agent system',
      version: '1.0.0',
      endpoints: {
        metrics: '/metrics',
        health: '/api/monitoring/health',
        summary: '/api/monitoring/summary'
      }
    };
  }

  /**
   * Get current metrics as JSON
   */
  async getCurrentMetrics(): Promise<Record<string, any>> {
    const metricsString = await this.collector.getMetrics();

    // Parse Prometheus format to JSON (simplified)
    const metrics: Record<string, any> = {};
    const lines = metricsString.split('\n');

    for (const line of lines) {
      if (line.startsWith('#') || line.trim() === '') continue;

      const parts = line.split(' ');
      if (parts.length >= 2) {
        const metricName = parts[0];
        const value = parseFloat(parts[1]);

        if (!isNaN(value)) {
          metrics[metricName] = value;
        }
      }
    }

    return {
      timestamp: new Date().toISOString(),
      metrics
    };
  }
}

// ========== SIMPLE IN-MEMORY METRICS (FALLBACK) ==========

interface SimpleMetrics {
  requests_total: number;
  requests_duration: number[];
  errors_total: number;
  uptime_start: number;
}

const _simpleMetrics: SimpleMetrics = {
  requests_total: 0,
  requests_duration: [],
  errors_total: 0,
  uptime_start: Date.now()
};

/**
 * Simple metrics collection (fallback when Prometheus is not available)
 */
export function getSimpleMetrics(): Record<string, any> {
  const uptime = (Date.now() - _simpleMetrics.uptime_start) / 1000;
  const avgDuration = _simpleMetrics.requests_duration.length > 0
    ? _simpleMetrics.requests_duration.reduce((a, b) => a + b, 0) / _simpleMetrics.requests_duration.length
    : 0;

  return {
    requests_total: _simpleMetrics.requests_total,
    errors_total: _simpleMetrics.errors_total,
    uptime_seconds: uptime,
    average_request_duration: avgDuration
  };
}

/**
 * Record simple metrics
 */
export function recordSimpleMetrics(duration: number, isError: boolean = false): void {
  _simpleMetrics.requests_total++;
  _simpleMetrics.requests_duration.push(duration);

  if (isError) {
    _simpleMetrics.errors_total++;
  }

  // Keep only last 1000 durations
  if (_simpleMetrics.requests_duration.length > 1000) {
    _simpleMetrics.requests_duration = _simpleMetrics.requests_duration.slice(-1000);
  }
}

// ========== EXPORTS ==========

/**
 * Global metrics collector instance
 */
export const metricsCollector = new MetricsCollector();

/**
 * Global monitoring service instance
 */
export const monitoringService = new MonitoringService(metricsCollector);

/**
 * Health check function
 */
export async function healthCheck(): Promise<{ status: string; timestamp: Date; details?: any }> {
  try {
    const health = monitoringService.getSystemHealth();
    return {
      status: 'healthy',
      timestamp: new Date(),
      details: health
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date(),
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Express middleware for metrics collection
 */
export function metricsMiddleware() {
  return (req: any, res: any, next: any) => {
    const startTime = Date.now();

    res.on('finish', () => {
      const duration = (Date.now() - startTime) / 1000;
      const method = req.method;
      const endpoint = req.route?.path || req.path;
      const status = res.statusCode;

      metricsCollector.recordHttpRequest(method, endpoint, status, duration);
      recordSimpleMetrics(duration, status >= 400);
    });

    next();
  };
}