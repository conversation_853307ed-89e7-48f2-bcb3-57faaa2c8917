/**
 * Database connection and management module
 * Implements PostgreSQL with pgvector for AI agent memory system
 * Converted from Python asyncpg/SQLAlchemy to TypeScript pg/TypeORM
 */

import { Pool, PoolClient, QueryResult } from 'pg';
import { DataSource, Repository, EntityManager } from 'typeorm';
import { Logger } from 'winston';
import { getSettings } from './config';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

const settings = getSettings();

// Global database connections
let _dataSource: DataSource | null = null;
let _connectionPool: Pool | null = null;

/**
 * Initialize database connections and pools
 */
export async function initDatabase(): Promise<void> {
  try {
    // Create TypeORM DataSource for ORM operations
    _dataSource = new DataSource({
      type: 'postgres',
      url: settings.database_url,
      synchronize: false, // Don't auto-sync in production
      logging: settings.isDevelopment,
      entities: [], // Add entity classes here
      migrations: [], // Add migration files here
      extra: {
        max: settings.database_pool_size,
        connectionTimeoutMillis: 60000,
        idleTimeoutMillis: 30000,
        application_name: 'ai_agent_service'
      }
    });

    await _dataSource.initialize();

    // Create pg Pool for raw SQL operations
    _connectionPool = new Pool({
      connectionString: settings.database_url,
      min: 5,
      max: settings.database_pool_size,
      connectionTimeoutMillis: 60000,
      idleTimeoutMillis: 30000,
      application_name: 'ai_agent_service'
    });

    // Test connection
    const client = await _connectionPool.connect();
    await client.query('SELECT 1');
    client.release();

    logger.info('Database initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize database', { error: error instanceof Error ? error.message : error });
    throw error;
  }
}

/**
 * Close database connections
 */
export async function closeDatabase(): Promise<void> {
  if (_connectionPool) {
    await _connectionPool.end();
    logger.info('Database connection pool closed');
  }

  if (_dataSource && _dataSource.isInitialized) {
    await _dataSource.destroy();
    logger.info('Database DataSource destroyed');
  }
}

/**
 * Get database session with automatic cleanup
 */
export async function getDbSession(): Promise<EntityManager> {
  if (!_dataSource || !_dataSource.isInitialized) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }

  return _dataSource.manager;
}

/**
 * Get database connection pool
 */
export async function getDatabase(): Promise<Pool> {
  if (!_connectionPool) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }

  return _connectionPool;
}

/**
 * Database service class for complex operations
 */
export class DatabaseService {
  private pool: Pool | null = null;

  constructor() {
    this.pool = null;
  }

  /**
   * Get database pool instance
   */
  private async getPool(): Promise<Pool> {
    if (!this.pool) {
      this.pool = await getDatabase();
    }
    return this.pool;
  }

  /**
   * Execute a query with parameters and return multiple results
   */
  async executeQuery<T = any>(query: string, params?: any[]): Promise<T[]> {
    const pool = await this.getPool();
    const client = await pool.connect();

    try {
      const result: QueryResult<T> = await client.query(query, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Execute a query and return single result
   */
  async executeSingleQuery<T = any>(query: string, params?: any[]): Promise<T | null> {
    const pool = await this.getPool();
    const client = await pool.connect();

    try {
      const result: QueryResult<T> = await client.query(query, params);
      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  /**
   * Execute a command (INSERT, UPDATE, DELETE)
   */
  async executeCommand(command: string, params?: any[]): Promise<QueryResult> {
    const pool = await this.getPool();
    const client = await pool.connect();

    try {
      return await client.query(command, params);
    } finally {
      client.release();
    }
  }

  /**
   * Execute multiple commands in a transaction
   */
  async executeTransaction(commands: Array<{ command: string; params?: any[] }>): Promise<QueryResult[]> {
    const pool = await this.getPool();
    const client = await pool.connect();

    try {
      await client.query('BEGIN');
      const results: QueryResult[] = [];

      for (const { command, params } of commands) {
        const result = await client.query(command, params);
        results.push(result);
      }

      await client.query('COMMIT');
      return results;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

// ========== VECTOR SIMILARITY SEARCH UTILITIES ==========

/**
 * Utilities for pgvector similarity search
 */
export class VectorSearch {
  /**
   * Perform vector similarity search
   */
  static async similaritySearch<T = any>(
    table: string,
    embeddingColumn: string,
    queryEmbedding: number[],
    limit: number = 10,
    similarityThreshold: number = 0.7
  ): Promise<T[]> {
    const pool = await getDatabase();

    const query = `
      SELECT *, 1 - (${embeddingColumn} <=> $1::vector) AS similarity
      FROM ${table}
      WHERE 1 - (${embeddingColumn} <=> $1::vector) > $2
      ORDER BY ${embeddingColumn} <=> $1::vector
      LIMIT $3
    `;

    const client = await pool.connect();
    try {
      const result = await client.query(query, [
        JSON.stringify(queryEmbedding),
        similarityThreshold,
        limit
      ]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Perform hybrid vector + text search
   */
  static async hybridSearch<T = any>(
    table: string,
    embeddingColumn: string,
    textColumn: string,
    queryEmbedding: number[],
    queryText: string,
    limit: number = 10,
    vectorWeight: number = 0.7,
    textWeight: number = 0.3
  ): Promise<T[]> {
    const pool = await getDatabase();

    const query = `
      SELECT *,
             ($4 * (1 - (${embeddingColumn} <=> $1::vector))) +
             ($5 * ts_rank_cd(to_tsvector('english', ${textColumn}), plainto_tsquery('english', $2)))
             AS combined_score
      FROM ${table}
      WHERE to_tsvector('english', ${textColumn}) @@ plainto_tsquery('english', $2)
         OR 1 - (${embeddingColumn} <=> $1::vector) > 0.5
      ORDER BY combined_score DESC
      LIMIT $3
    `;

    const client = await pool.connect();
    try {
      const result = await client.query(query, [
        JSON.stringify(queryEmbedding),
        queryText,
        limit,
        vectorWeight,
        textWeight
      ]);
      return result.rows;
    } finally {
      client.release();
    }
  }
}

// ========== MEMORY-SPECIFIC DATABASE OPERATIONS ==========

/**
 * Database operations for memory management
 */
export class MemoryDatabase {
  /**
   * Store a memory with embedding
   */
  static async storeMemory(
    content: string,
    embedding: number[],
    memoryType: string,
    metadata: Record<string, any>,
    importanceScore: number = 0.5
  ): Promise<string> {
    const pool = await getDatabase();

    const query = `
      INSERT INTO agent_memory.memories
      (content, embedding, memory_type, metadata, importance_score)
      VALUES ($1, $2::vector, $3, $4, $5)
      RETURNING id
    `;

    const client = await pool.connect();
    try {
      const result = await client.query(query, [
        content,
        JSON.stringify(embedding),
        memoryType,
        JSON.stringify(metadata),
        importanceScore
      ]);
      return result.rows[0].id;
    } finally {
      client.release();
    }
  }

  /**
   * Search memories by embedding similarity
   */
  static async searchMemories<T = any>(
    queryEmbedding: number[],
    limit: number = 10,
    memoryType?: string,
    importanceThreshold: number = 0.3
  ): Promise<T[]> {
    const pool = await getDatabase();

    const conditions = ['importance_score >= $3'];
    const params: any[] = [JSON.stringify(queryEmbedding), limit, importanceThreshold];

    if (memoryType) {
      conditions.push('memory_type = $4');
      params.push(memoryType);
    }

    const query = `
      SELECT id, content, metadata, memory_type, importance_score,
             1 - (embedding <=> $1::vector) AS similarity,
             access_count, last_accessed, created_at
      FROM agent_memory.memories
      WHERE ${conditions.join(' AND ')}
      ORDER BY embedding <=> $1::vector
      LIMIT $2
    `;

    const client = await pool.connect();
    try {
      const result = await client.query(query, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Update memory access tracking
   */
  static async updateMemoryAccess(memoryId: string): Promise<void> {
    const pool = await getDatabase();

    const query = `
      UPDATE agent_memory.memories
      SET access_count = access_count + 1, last_accessed = NOW()
      WHERE id = $1
    `;

    const client = await pool.connect();
    try {
      await client.query(query, [memoryId]);
    } finally {
      client.release();
    }
  }

  /**
   * Consolidate and cluster memories
   */
  static async consolidateMemories(batchSize: number = 100): Promise<number> {
    const pool = await getDatabase();

    // Get memories that need consolidation
    const query = `
      SELECT id, content, embedding, metadata, importance_score
      FROM agent_memory.memories
      WHERE last_accessed > NOW() - INTERVAL '24 hours'
      ORDER BY importance_score DESC, access_count DESC
      LIMIT $1
    `;

    const client = await pool.connect();
    try {
      const result = await client.query(query, [batchSize]);
      const memories = result.rows;

      // Simple clustering logic - group similar memories
      // This would be enhanced with more sophisticated clustering algorithms
      if (memories.length > 1) {
        // Update memory clusters
        const clusterQuery = `
          INSERT INTO agent_memory.memory_clusters
          (name, description, memory_ids, cluster_metadata)
          VALUES ($1, $2, $3, $4)
        `;

        const memoryIds = memories.map(m => m.id);
        await client.query(clusterQuery, [
          `Auto-cluster-${memoryIds.length}`,
          `Automatically generated cluster of ${memoryIds.length} related memories`,
          memoryIds,
          JSON.stringify({ auto_generated: true, size: memoryIds.length })
        ]);
      }

      logger.info(`Consolidated ${memories.length} memories`);
      return memories.length;
    } finally {
      client.release();
    }
  }
}

// ========== TYPE DEFINITIONS ==========

/**
 * Memory record interface
 */
export interface MemoryRecord {
  id: string;
  content: string;
  embedding: number[];
  metadata: Record<string, any>;
  memory_type: string;
  source?: string;
  importance_score: number;
  access_count: number;
  last_accessed: Date;
  created_at: Date;
  expires_at?: Date;
  similarity?: number;
}

/**
 * Memory cluster interface
 */
export interface MemoryCluster {
  id: string;
  name: string;
  description?: string;
  centroid: number[];
  memory_ids: string[];
  cluster_metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

/**
 * Database connection options
 */
export interface DatabaseOptions {
  url: string;
  poolSize: number;
  maxOverflow: number;
  connectionTimeout: number;
  idleTimeout: number;
  applicationName: string;
}

/**
 * Query result with similarity score
 */
export interface SimilarityResult<T = any> extends T {
  similarity: number;
}

/**
 * Hybrid search result with combined score
 */
export interface HybridSearchResult<T = any> extends T {
  combined_score: number;
}

// ========== EXPORT INSTANCES ==========

/**
 * Export database service instance
 */
export const dbService = new DatabaseService();

/**
 * Export vector search utilities
 */
export const vectorSearch = VectorSearch;

/**
 * Export memory database utilities
 */
export const memoryDb = MemoryDatabase;

/**
 * Database health check
 */
export async function healthCheck(): Promise<{ status: string; timestamp: Date; details?: any }> {
  try {
    const pool = await getDatabase();
    const client = await pool.connect();

    try {
      const result = await client.query('SELECT NOW() as timestamp, version() as version');
      client.release();

      return {
        status: 'healthy',
        timestamp: new Date(),
        details: {
          database_time: result.rows[0].timestamp,
          database_version: result.rows[0].version,
          pool_total: pool.totalCount,
          pool_idle: pool.idleCount,
          pool_waiting: pool.waitingCount
        }
      };
    } catch (error) {
      client.release();
      throw error;
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date(),
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Initialize database with retry logic
 */
export async function initDatabaseWithRetry(maxRetries: number = 3, retryDelay: number = 1000): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await initDatabase();
      logger.info(`Database initialized successfully on attempt ${attempt}`);
      return;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      logger.warn(`Database initialization attempt ${attempt} failed: ${lastError.message}`);

      if (attempt < maxRetries) {
        logger.info(`Retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryDelay *= 2; // Exponential backoff
      }
    }
  }

  throw new Error(`Failed to initialize database after ${maxRetries} attempts. Last error: ${lastError?.message}`);
}