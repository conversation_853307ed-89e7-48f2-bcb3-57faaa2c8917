/**
 * Perplexity API service for AI-powered research and market analysis - TypeScript version
 * Implements Factor 1: Natural Language to Tool Calls with structured outputs
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  PerplexityMessage,
  PerplexityTool,
  PerplexityResponse,
  MarketIntelligence,
  MarketResearchResult,
  VendorInfo,
  VendorDiscoveryResult,
  BaseMetadata,
  Logger,
  ServiceConfig
} from './types';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

// Mock settings - replace with actual configuration
const settings: ServiceConfig = {
  perplexity_api_key: process.env.PERPLEXITY_API_KEY || 'your-api-key',
  perplexity_model: process.env.PERPLEXITY_MODEL || 'llama-3.1-sonar-small-128k-online',
  perplexity_max_tokens: parseInt(process.env.PERPLEXITY_MAX_TOKENS || '2000'),
  perplexity_temperature: parseFloat(process.env.PERPLEXITY_TEMPERATURE || '0.7')
};

export class PerplexityService {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly model: string;
  private readonly maxTokens: number;
  private readonly temperature: number;
  private readonly httpClient: AxiosInstance;

  constructor() {
    this.apiKey = settings.perplexity_api_key;
    this.baseUrl = "https://api.perplexity.ai";
    this.model = settings.perplexity_model;
    this.maxTokens = settings.perplexity_max_tokens;
    this.temperature = settings.perplexity_temperature;

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 60000,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Make request to Perplexity API
   */
  private async makeRequest(
    messages: PerplexityMessage[], 
    tools?: PerplexityTool[]
  ): Promise<PerplexityResponse> {
    const payload: any = {
      model: `perplexity/${this.model}`,
      messages,
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      stream: false
    };

    if (tools) {
      payload.tools = tools;
    }

    try {
      const response: AxiosResponse<PerplexityResponse> = await this.httpClient.post(
        '/chat/completions',
        payload
      );
      return response.data;
    } catch (error: any) {
      if (error.response) {
        logger.error("Perplexity API request failed", { 
          status: error.response.status,
          data: error.response.data 
        });
      } else {
        logger.error("Unexpected error in Perplexity request", { error: String(error) });
      }
      throw error;
    }
  }

  /**
   * Conduct market research using Perplexity
   * Implements structured output for market intelligence
   */
  async researchMarket(
    query: string, 
    context?: Array<{ content: string }>
  ): Promise<MarketResearchResult> {
    logger.info("Conducting market research", { query: query.substring(0, 100) });

    // Prepare context from memory if available
    let contextText = "";
    if (context) {
      contextText = context.slice(0, 3).map(item => item.content).join("\n");
    }

    const systemPrompt = `You are a procurement market research specialist. 
    Analyze the given procurement request and provide comprehensive market intelligence.
    
    Focus on:
    1. Current market prices and price ranges
    2. Market trends and growth patterns
    3. Key suppliers and vendor recommendations
    4. Risk factors and challenges
    5. Compliance and regulatory requirements
    
    Provide specific, actionable insights based on current market data.`;

    const userPrompt = `Research market conditions for this procurement request:
    
    Request: ${query}
    
    Additional Context: ${contextText}
    
    Provide detailed market intelligence including pricing, trends, suppliers, risks, and compliance requirements.`;

    const messages: PerplexityMessage[] = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ];

    // Define structured output tool
    const marketResearchTool: PerplexityTool = {
      type: "function",
      function: {
        name: "provide_market_intelligence",
        description: "Provide structured market intelligence for procurement",
        parameters: {
          type: "object",
          properties: {
            price_range: {
              type: "string",
              description: "Estimated price range for the requested items"
            },
            market_trends: {
              type: "string", 
              description: "Current market trends and growth patterns"
            },
            supplier_recommendations: {
              type: "array",
              items: { type: "string" },
              description: "List of recommended suppliers or supplier types"
            },
            risk_factors: {
              type: "array",
              items: { type: "string" },
              description: "Key risk factors to consider"
            },
            compliance_requirements: {
              type: "array",
              items: { type: "string" },
              description: "Relevant compliance and regulatory requirements"
            },
            market_insights: {
              type: "string",
              description: "Additional market insights and recommendations"
            }
          },
          required: ["price_range", "market_trends", "supplier_recommendations", "risk_factors"]
        }
      }
    };

    try {
      const response = await this.makeRequest(messages, [marketResearchTool]);

      // Extract structured data from tool call or content
      if (response.choices && response.choices.length > 0) {
        const choice = response.choices[0];

        // Check for tool calls
        if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
          const toolCall = choice.message.tool_calls[0];
          if (toolCall.function.name === "provide_market_intelligence") {
            const structuredData: MarketIntelligence = JSON.parse(toolCall.function.arguments);
            return {
              content: choice.message.content || "",
              structured_data: structuredData,
              timestamp: new Date().toISOString()
            };
          }
        }

        // Fallback to content-based parsing
        const content = choice.message.content;
        return {
          content,
          structured_data: this.parseMarketIntelligence(content),
          timestamp: new Date().toISOString()
        };
      }

      return {
        content: "Market research completed",
        structured_data: {} as MarketIntelligence,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error("Market research failed", { error: String(error) });
      return {
        content: `Market research failed: ${String(error)}`,
        structured_data: {} as MarketIntelligence,
        timestamp: new Date().toISOString(),
        error: String(error)
      };
    }
  }

  /**
   * Discover vendors using Perplexity research
   */
  async discoverVendors(
    query: string,
    context?: Array<{ content: string }>
  ): Promise<VendorDiscoveryResult> {
    logger.info("Discovering vendors", { query: query.substring(0, 100) });

    let contextText = "";
    if (context) {
      contextText = context.slice(0, 3).map(item => item.content).join("\n");
    }

    const systemPrompt = `You are a vendor discovery specialist. 
    Research and identify potential suppliers for procurement requests.
    
    Focus on:
    1. Reputable suppliers in the specified region
    2. Supplier specialties and capabilities
    3. Supplier ratings and reputation
    4. Contact information when available
    5. Supplier strengths and differentiators`;

    const userPrompt = `Find suppliers for this procurement request:
    
    Request: ${query}
    
    Context: ${contextText}
    
    Identify specific suppliers with their capabilities, reputation, and contact details if available.`;

    const messages: PerplexityMessage[] = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ];

    const vendorDiscoveryTool: PerplexityTool = {
      type: "function",
      function: {
        name: "identify_vendors",
        description: "Identify and recommend vendors for procurement",
        parameters: {
          type: "object",
          properties: {
            vendors: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  specialties: {
                    type: "array",
                    items: { type: "string" }
                  },
                  location: { type: "string" },
                  reputation: { type: "string" },
                  contact_info: { type: "string" }
                }
              }
            },
            vendor_insights: {
              type: "string",
              description: "Additional insights about the vendor landscape"
            }
          },
          required: ["vendors", "vendor_insights"]
        }
      }
    };

    try {
      const response = await this.makeRequest(messages, [vendorDiscoveryTool]);

      if (response.choices && response.choices.length > 0) {
        const choice = response.choices[0];

        if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
          const toolCall = choice.message.tool_calls[0];
          if (toolCall.function.name === "identify_vendors") {
            const structuredData = JSON.parse(toolCall.function.arguments);
            return {
              content: choice.message.content || "",
              vendors: structuredData.vendors || [],
              insights: structuredData.vendor_insights || "",
              timestamp: new Date().toISOString()
            };
          }
        }

        const content = choice.message.content;
        return {
          content,
          vendors: this.parseVendors(content),
          insights: content,
          timestamp: new Date().toISOString()
        };
      }

      return {
        content: "Vendor discovery completed",
        vendors: [],
        insights: "",
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error("Vendor discovery failed", { error: String(error) });
      return {
        content: `Vendor discovery failed: ${String(error)}`,
        vendors: [],
        insights: "",
        timestamp: new Date().toISOString(),
        error: String(error)
      };
    }
  }

  /**
   * Generate executive summary using Perplexity
   */
  async generateSummary(workflowData: BaseMetadata): Promise<string> {
    logger.info("Generating workflow summary");

    const systemPrompt = `You are an executive summary specialist.
    Create concise, professional summaries of business workflows.
    Focus on key results, metrics, and next steps.`;

    const userPrompt = `Create an executive summary for this RFQ workflow:
    
    Workflow Data: ${JSON.stringify(workflowData, null, 2).substring(0, 2000)}
    
    Include key milestones, results, vendor information, and recommended next steps.
    Format as a professional business report.`;

    const messages: PerplexityMessage[] = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ];

    try {
      const response = await this.makeRequest(messages);

      if (response.choices && response.choices.length > 0) {
        return response.choices[0].message.content;
      }

      return "Executive summary generation completed";

    } catch (error) {
      logger.error("Summary generation failed", { error: String(error) });
      return `Summary generation failed: ${String(error)}`;
    }
  }

  /**
   * Parse market intelligence from unstructured content
   */
  private parseMarketIntelligence(content: string): MarketIntelligence {
    // Simple content parsing - can be enhanced with more sophisticated NLP
    const lines = content.split('\n');

    const intelligence: MarketIntelligence = {
      price_range: "Price analysis available",
      market_trends: "Market trends analysis available", 
      supplier_recommendations: [],
      risk_factors: [],
      compliance_requirements: []
    };

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      if (trimmedLine.toLowerCase().includes('price') && trimmedLine.includes(':')) {
        intelligence.price_range = trimmedLine.split(':', 2)[1].trim();
      } else if (trimmedLine.toLowerCase().includes('trend') && trimmedLine.includes(':')) {
        intelligence.market_trends = trimmedLine.split(':', 2)[1].trim();
      } else if (trimmedLine.toLowerCase().includes('supplier') || trimmedLine.toLowerCase().includes('vendor')) {
        if (trimmedLine.includes(':')) {
          intelligence.supplier_recommendations.push(trimmedLine.split(':', 2)[1].trim());
        }
      } else if (trimmedLine.toLowerCase().includes('risk')) {
        if (trimmedLine.includes(':')) {
          intelligence.risk_factors.push(trimmedLine.split(':', 2)[1].trim());
        }
      } else if (trimmedLine.toLowerCase().includes('compliance') || trimmedLine.toLowerCase().includes('regulation')) {
        if (trimmedLine.includes(':')) {
          intelligence.compliance_requirements!.push(trimmedLine.split(':', 2)[1].trim());
        }
      }
    }

    return intelligence;
  }

  /**
   * Parse vendor information from content
   */
  private parseVendors(content: string): VendorInfo[] {
    // Simple vendor parsing - can be enhanced
    const vendors: VendorInfo[] = [];
    const lines = content.split('\n');

    let currentVendor: Partial<VendorInfo> = {};
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) {
        if (currentVendor.name) {
          vendors.push(currentVendor as VendorInfo);
          currentVendor = {};
        }
        continue;
      }

      if (trimmedLine.toLowerCase().includes('name:')) {
        currentVendor.name = trimmedLine.split(':', 2)[1].trim();
      } else if (trimmedLine.toLowerCase().includes('location:')) {
        currentVendor.location = trimmedLine.split(':', 2)[1].trim();
      } else if (trimmedLine.toLowerCase().includes('specialt')) {
        currentVendor.specialties = [trimmedLine.split(':', 2)[1].trim()];
      }
    }

    if (currentVendor.name) {
      vendors.push(currentVendor as VendorInfo);
    }

    return vendors.slice(0, 5); // Limit to 5 vendors
  }

  /**
   * Get service information
   */
  getServiceInfo(): { model: string; version: string } {
    return {
      model: this.model,
      version: "1.0.0"
    };
  }
}
