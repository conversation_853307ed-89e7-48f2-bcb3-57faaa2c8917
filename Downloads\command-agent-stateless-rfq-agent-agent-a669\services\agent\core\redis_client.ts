/**
 * Redis client module for caching and state management
 * Implements high-performance caching layer for AI agent system
 * Converted from Python redis.asyncio to TypeScript ioredis
 */

import Redis from 'ioredis';
import { Logger } from 'winston';
import { getSettings } from './config';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

const settings = getSettings();

// Global Redis client
let _redisClient: Redis | null = null;

/**
 * Initialize Redis connection
 */
export async function initRedis(): Promise<void> {
  try {
    _redisClient = new Redis(settings.redis_url, {
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: null,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
      family: 4, // IPv4
    });

    // Test connection
    await _redisClient.ping();
    logger.info('Redis initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize Redis', { error: error instanceof Error ? error.message : error });
    throw error;
  }
}

/**
 * Close Redis connection
 */
export async function closeRedis(): Promise<void> {
  if (_redisClient) {
    await _redisClient.quit();
    logger.info('Redis connection closed');
  }
}

/**
 * Get Redis client instance
 */
export async function getRedis(): Promise<Redis> {
  if (!_redisClient) {
    throw new Error('Redis not initialized. Call initRedis() first.');
  }

  return _redisClient;
}

/**
 * Redis service for caching and state management
 */
export class RedisService {
  private client: Redis | null = null;

  constructor() {
    this.client = null;
  }

  /**
   * Get Redis client
   */
  async getClient(): Promise<Redis> {
    if (!this.client) {
      this.client = await getRedis();
    }
    return this.client;
  }

  // ========== BASIC OPERATIONS ==========

  /**
   * Set a key with optional expiration
   */
  async set(
    key: string,
    value: any,
    ex?: number,
    serialization: 'json' | 'string' = 'json'
  ): Promise<'OK' | null> {
    const client = await this.getClient();

    let serializedValue: string;
    if (serialization === 'json') {
      serializedValue = JSON.stringify(value);
    } else {
      serializedValue = String(value);
    }

    if (ex) {
      return await client.setex(key, ex, serializedValue);
    } else {
      return await client.set(key, serializedValue);
    }
  }

  /**
   * Get a value by key
   */
  async get<T = any>(
    key: string,
    serialization: 'json' | 'string' = 'json'
  ): Promise<T | null> {
    const client = await this.getClient();
    const value = await client.get(key);

    if (value === null) {
      return null;
    }

    if (serialization === 'json') {
      try {
        return JSON.parse(value);
      } catch (error) {
        logger.warn(`Failed to parse JSON for key ${key}`, { error });
        return value as T;
      }
    } else {
      return value as T;
    }
  }

  /**
   * Delete one or more keys
   */
  async delete(...keys: string[]): Promise<number> {
    const client = await this.getClient();
    return await client.del(...keys);
  }

  /**
   * Check if keys exist
   */
  async exists(...keys: string[]): Promise<number> {
    const client = await this.getClient();
    return await client.exists(...keys);
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, seconds: number): Promise<number> {
    const client = await this.getClient();
    return await client.expire(key, seconds);
  }

  /**
   * Get time to live for a key
   */
  async ttl(key: string): Promise<number> {
    const client = await this.getClient();
    return await client.ttl(key);
  }

  // ========== HASH OPERATIONS ==========

  /**
   * Set hash fields
   */
  async hset(name: string, mapping: Record<string, any>): Promise<number> {
    const client = await this.getClient();
    const serializedMapping: Record<string, string> = {};

    for (const [k, v] of Object.entries(mapping)) {
      serializedMapping[k] = typeof v === 'string' ? v : JSON.stringify(v);
    }

    return await client.hset(name, serializedMapping);
  }

  /**
   * Get hash field value
   */
  async hget<T = any>(name: string, key: string): Promise<T | null> {
    const client = await this.getClient();
    const value = await client.hget(name, key);

    if (value === null) {
      return null;
    }

    try {
      return JSON.parse(value);
    } catch (error) {
      return value as T;
    }
  }

  /**
   * Get all hash fields and values
   */
  async hgetall<T = Record<string, any>>(name: string): Promise<T> {
    const client = await this.getClient();
    const data = await client.hgetall(name);
    const result: Record<string, any> = {};

    for (const [k, v] of Object.entries(data)) {
      try {
        result[k] = JSON.parse(v);
      } catch (error) {
        result[k] = v;
      }
    }

    return result as T;
  }

  /**
   * Delete hash fields
   */
  async hdel(name: string, ...keys: string[]): Promise<number> {
    const client = await this.getClient();
    return await client.hdel(name, ...keys);
  }

  // ========== LIST OPERATIONS ==========

  /**
   * Push values to the left of a list
   */
  async lpush(name: string, ...values: any[]): Promise<number> {
    const client = await this.getClient();
    const serializedValues = values.map(v =>
      typeof v === 'string' ? v : JSON.stringify(v)
    );
    return await client.lpush(name, ...serializedValues);
  }

  /**
   * Push values to the right of a list
   */
  async rpush(name: string, ...values: any[]): Promise<number> {
    const client = await this.getClient();
    const serializedValues = values.map(v =>
      typeof v === 'string' ? v : JSON.stringify(v)
    );
    return await client.rpush(name, ...serializedValues);
  }

  /**
   * Pop value from the left of a list
   */
  async lpop<T = any>(name: string): Promise<T | null> {
    const client = await this.getClient();
    const value = await client.lpop(name);

    if (value === null) {
      return null;
    }

    try {
      return JSON.parse(value);
    } catch (error) {
      return value as T;
    }
  }

  /**
   * Pop value from the right of a list
   */
  async rpop<T = any>(name: string): Promise<T | null> {
    const client = await this.getClient();
    const value = await client.rpop(name);

    if (value === null) {
      return null;
    }

    try {
      return JSON.parse(value);
    } catch (error) {
      return value as T;
    }
  }

  /**
   * Get a range of values from a list
   */
  async lrange<T = any>(name: string, start: number, end: number): Promise<T[]> {
    const client = await this.getClient();
    const values = await client.lrange(name, start, end);

    return values.map(value => {
      try {
        return JSON.parse(value);
      } catch (error) {
        return value as T;
      }
    });
  }

  // ========== SET OPERATIONS ==========

  /**
   * Add values to a set
   */
  async sadd(name: string, ...values: any[]): Promise<number> {
    const client = await this.getClient();
    const serializedValues = values.map(v =>
      typeof v === 'string' ? v : JSON.stringify(v)
    );
    return await client.sadd(name, ...serializedValues);
  }

  /**
   * Get all members of a set
   */
  async smembers<T = any>(name: string): Promise<Set<T>> {
    const client = await this.getClient();
    const values = await client.smembers(name);
    const result = new Set<T>();

    for (const value of values) {
      try {
        result.add(JSON.parse(value));
      } catch (error) {
        result.add(value as T);
      }
    }

    return result;
  }

  /**
   * Remove values from a set
   */
  async srem(name: string, ...values: any[]): Promise<number> {
    const client = await this.getClient();
    const serializedValues = values.map(v =>
      typeof v === 'string' ? v : JSON.stringify(v)
    );
    return await client.srem(name, ...serializedValues);
  }
}

// ========== CACHE MANAGER ==========

/**
 * Cache manager for common caching patterns
 */
export class CacheManager {
  constructor(private redis: RedisService) {}

  /**
   * Cache function result
   */
  async cachedFunction<T>(
    cacheKey: string,
    func: (...args: any[]) => T | Promise<T>,
    args: any[] = [],
    ttl: number = 3600
  ): Promise<T> {
    // Check cache first
    const cachedResult = await this.redis.get<T>(cacheKey);
    if (cachedResult !== null) {
      logger.debug('Cache hit', { cache_key: cacheKey });
      return cachedResult;
    }

    // Execute function and cache result
    logger.debug('Cache miss, executing function', { cache_key: cacheKey });
    const result = await func(...args);

    await this.redis.set(cacheKey, result, ttl);
    return result;
  }

  /**
   * Invalidate all keys matching a pattern
   */
  async invalidatePattern(pattern: string): Promise<void> {
    const client = await this.redis.getClient();
    const keys = await client.keys(pattern);

    if (keys.length > 0) {
      await client.del(...keys);
      logger.info(`Invalidated ${keys.length} cache keys`, { pattern });
    }
  }
}

// ========== WORKFLOW STATE MANAGER ==========

/**
 * Manage workflow state in Redis
 */
export class WorkflowStateManager {
  private readonly statePrefix = 'workflow:state:';
  private readonly checkpointPrefix = 'workflow:checkpoint:';

  constructor(private redis: RedisService) {}

  /**
   * Save workflow state
   */
  async saveState(
    executionId: string,
    state: Record<string, any>,
    ttl: number = 86400 // 24 hours
  ): Promise<void> {
    const key = `${this.statePrefix}${executionId}`;
    await this.redis.set(key, state, ttl);
    logger.debug('Workflow state saved', { execution_id: executionId });
  }

  /**
   * Load workflow state
   */
  async loadState<T = Record<string, any>>(executionId: string): Promise<T | null> {
    const key = `${this.statePrefix}${executionId}`;
    const state = await this.redis.get<T>(key);

    if (state) {
      logger.debug('Workflow state loaded', { execution_id: executionId });
    }

    return state;
  }

  /**
   * Delete workflow state
   */
  async deleteState(executionId: string): Promise<void> {
    const key = `${this.statePrefix}${executionId}`;
    await this.redis.delete(key);
    logger.debug('Workflow state deleted', { execution_id: executionId });
  }

  /**
   * Create a workflow checkpoint
   */
  async createCheckpoint(
    executionId: string,
    stepName: string,
    checkpointData: Record<string, any>
  ): Promise<void> {
    const key = `${this.checkpointPrefix}${executionId}:${stepName}`;
    await this.redis.set(key, checkpointData, 86400); // 24 hours

    // Add to checkpoint list
    const checkpointListKey = `${this.checkpointPrefix}list:${executionId}`;
    await this.redis.lpush(checkpointListKey, stepName);
    await this.redis.expire(checkpointListKey, 86400);

    logger.debug('Checkpoint created', {
      execution_id: executionId,
      step_name: stepName
    });
  }

  /**
   * Load a specific checkpoint
   */
  async loadCheckpoint<T = Record<string, any>>(
    executionId: string,
    stepName: string
  ): Promise<T | null> {
    const key = `${this.checkpointPrefix}${executionId}:${stepName}`;
    return await this.redis.get<T>(key);
  }

  /**
   * List all checkpoints for an execution
   */
  async listCheckpoints(executionId: string): Promise<string[]> {
    const checkpointListKey = `${this.checkpointPrefix}list:${executionId}`;
    return await this.redis.lrange<string>(checkpointListKey, 0, -1);
  }
}

// ========== SESSION MANAGER ==========

/**
 * Manage user sessions and context
 */
export class SessionManager {
  private readonly sessionPrefix = 'session:';
  private readonly contextPrefix = 'context:';

  constructor(private redis: RedisService) {}

  /**
   * Create a new session
   */
  async createSession(
    userId: string,
    sessionData: Record<string, any>,
    ttl: number = 3600
  ): Promise<string> {
    const { v4: uuidv4 } = await import('uuid');
    const sessionId = uuidv4();
    const key = `${this.sessionPrefix}${sessionId}`;

    const sessionInfo = {
      user_id: userId,
      created_at: new Date().toISOString(),
      ...sessionData
    };

    await this.redis.set(key, sessionInfo, ttl);
    logger.info('Session created', { session_id: sessionId, user_id: userId });
    return sessionId;
  }

  /**
   * Get session data
   */
  async getSession<T = Record<string, any>>(sessionId: string): Promise<T | null> {
    const key = `${this.sessionPrefix}${sessionId}`;
    return await this.redis.get<T>(key);
  }

  /**
   * Update session data
   */
  async updateSession(
    sessionId: string,
    updates: Record<string, any>
  ): Promise<void> {
    const key = `${this.sessionPrefix}${sessionId}`;
    const currentData = await this.redis.get<Record<string, any>>(key);

    if (currentData) {
      const updatedData = { ...currentData, ...updates };
      await this.redis.set(key, updatedData);

      // Preserve existing TTL
      const ttl = await this.redis.ttl(key);
      if (ttl > 0) {
        await this.redis.expire(key, ttl);
      }
    }
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<void> {
    const key = `${this.sessionPrefix}${sessionId}`;
    await this.redis.delete(key);
    logger.info('Session deleted', { session_id: sessionId });
  }
}

// ========== TYPE DEFINITIONS ==========

/**
 * Redis connection options
 */
export interface RedisOptions {
  url: string;
  maxConnections: number;
  connectTimeout: number;
  commandTimeout: number;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
}

/**
 * Session data interface
 */
export interface SessionData {
  user_id: string;
  created_at: string;
  [key: string]: any;
}

/**
 * Workflow state interface
 */
export interface WorkflowState {
  execution_id: string;
  current_step: string;
  progress_percentage: number;
  [key: string]: any;
}

/**
 * Checkpoint data interface
 */
export interface CheckpointData {
  step_name: string;
  timestamp: string;
  state_snapshot: Record<string, any>;
  [key: string]: any;
}

// ========== EXPORT INSTANCES ==========

/**
 * Export Redis service instance
 */
export const redisService = new RedisService();

/**
 * Export cache manager instance
 */
export const cacheManager = new CacheManager(redisService);

/**
 * Export workflow state manager instance
 */
export const workflowStateManager = new WorkflowStateManager(redisService);

/**
 * Export session manager instance
 */
export const sessionManager = new SessionManager(redisService);

/**
 * Redis health check
 */
export async function redisHealthCheck(): Promise<{ status: string; timestamp: Date; details?: any }> {
  try {
    const client = await getRedis();
    const start = Date.now();
    await client.ping();
    const responseTime = Date.now() - start;

    const info = await client.info('memory');
    const memoryInfo = info.split('\r\n').reduce((acc, line) => {
      const [key, value] = line.split(':');
      if (key && value) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, string>);

    return {
      status: 'healthy',
      timestamp: new Date(),
      details: {
        response_time_ms: responseTime,
        used_memory: memoryInfo.used_memory_human,
        connected_clients: memoryInfo.connected_clients,
        total_commands_processed: memoryInfo.total_commands_processed
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date(),
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Initialize Redis with retry logic
 */
export async function initRedisWithRetry(maxRetries: number = 3, retryDelay: number = 1000): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await initRedis();
      logger.info(`Redis initialized successfully on attempt ${attempt}`);
      return;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      logger.warn(`Redis initialization attempt ${attempt} failed: ${lastError.message}`);

      if (attempt < maxRetries) {
        logger.info(`Retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryDelay *= 2; // Exponential backoff
      }
    }
  }

  throw new Error(`Failed to initialize Redis after ${maxRetries} attempts. Last error: ${lastError?.message}`);
}