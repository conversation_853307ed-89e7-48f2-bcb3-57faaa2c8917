/**
 * Memory Service for semantic memory management with pgvector - TypeScript version
 * Implements Factor 3: Own Your Context Window with intelligent memory retrieval
 */

import {
  MemoryItem,
  ConversationData,
  ConversationMemory,
  MemorySearchRequest,
  MemoryStatistics,
  EmbeddingVector,
  BaseMetadata,
  Logger,
  DatabaseQueryParams,
  DatabaseResult
} from './types';
import { EmbeddingService } from './embeddingService';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

// Mock database service - replace with actual database implementation
const mockDbService = {
  async executeQuery(query: string, params?: DatabaseQueryParams): Promise<DatabaseResult[]> {
    // Mock implementation
    return [];
  },
  async executeSingleQuery(query: string, params?: DatabaseQueryParams): Promise<DatabaseResult | null> {
    // Mock implementation
    return null;
  },
  async executeCommand(query: string, params?: DatabaseQueryParams): Promise<void> {
    // Mock implementation
  }
};

// Mock memory database - replace with actual implementation
const mockMemoryDb = {
  async storeMemory(
    content: string,
    embedding: EmbeddingVector,
    memoryType: string,
    metadata: BaseMetadata,
    importanceScore: number
  ): Promise<string> {
    // Mock implementation
    return `memory_${Date.now()}`;
  },
  async searchMemories(
    queryEmbedding: EmbeddingVector,
    limit: number,
    memoryType?: string,
    importanceThreshold?: number
  ): Promise<MemoryItem[]> {
    // Mock implementation
    return [];
  },
  async updateMemoryAccess(memoryId: string): Promise<void> {
    // Mock implementation
  },
  async consolidateMemories(batchSize: number): Promise<number> {
    // Mock implementation
    return 0;
  }
};

// Mock cache manager - replace with actual implementation
const mockCacheManager = {
  async invalidatePattern(pattern: string): Promise<void> {
    // Mock implementation
  },
  redis: {
    async get(key: string): Promise<any> {
      // Mock implementation
      return null;
    },
    async set(key: string, value: any, options?: { ex?: number }): Promise<void> {
      // Mock implementation
    }
  }
};

export class MemoryService {
  private readonly embeddingService: EmbeddingService;
  private readonly cacheTtl: number;

  constructor() {
    this.embeddingService = new EmbeddingService();
    this.cacheTtl = 3600; // 1 hour cache
  }

  /**
   * Store a memory with semantic embedding
   * Implements Factor 3: Context Window Management
   */
  async storeMemory(
    content: string,
    memoryType: string,
    metadata: BaseMetadata,
    importanceScore: number = 0.5,
    source: string = "workflow"
  ): Promise<string> {
    logger.info("Storing memory", { memory_type: memoryType, content_length: content.length });

    try {
      // Generate embedding for the content
      const embedding = await this.embeddingService.generateEmbedding(content);

      // Store in database
      const memoryId = await mockMemoryDb.storeMemory(
        content,
        embedding,
        memoryType,
        {
          ...metadata,
          source,
          created_by: "memory_service",
          content_length: content.length
        },
        importanceScore
      );

      // Invalidate related cache
      await mockCacheManager.invalidatePattern(`memory:search:*${memoryType}*`);

      logger.info("Memory stored successfully", { memory_id: memoryId, memory_type: memoryType });
      return memoryId;

    } catch (error) {
      logger.error("Failed to store memory", { error: String(error), memory_type: memoryType });
      throw error;
    }
  }

  /**
   * Retrieve relevant memories for context
   * Implements semantic search with caching
   */
  async retrieveContext(
    query: string,
    limit: number = 10,
    memoryTypes?: string[],
    importanceThreshold: number = 0.3
  ): Promise<MemoryItem[]> {
    logger.info("Retrieving memory context", { query: query.substring(0, 100), limit });

    try {
      // Generate cache key
      let cacheKey = `memory:search:${this.hashString(query)}:${limit}:${importanceThreshold}`;
      if (memoryTypes) {
        cacheKey += `:${memoryTypes.sort().join('_')}`;
      }

      // Check cache first
      const cachedResult = await mockCacheManager.redis.get(cacheKey);
      if (cachedResult) {
        logger.debug("Memory context cache hit", { query: query.substring(0, 50) });
        return cachedResult;
      }

      // Generate query embedding
      const queryEmbedding = await this.embeddingService.generateEmbedding(query);

      // Search memories
      let memories: MemoryItem[] = [];
      if (memoryTypes) {
        for (const memoryType of memoryTypes) {
          const typeMemories = await mockMemoryDb.searchMemories(
            queryEmbedding,
            Math.floor(limit / memoryTypes.length) + 1,
            memoryType,
            importanceThreshold
          );
          memories.push(...typeMemories);
        }
      } else {
        memories = await mockMemoryDb.searchMemories(
          queryEmbedding,
          limit,
          undefined,
          importanceThreshold
        );
      }

      // Sort by similarity and limit results
      memories.sort((a, b) => (b.similarity || 0) - (a.similarity || 0));
      memories = memories.slice(0, limit);

      // Update access tracking
      for (const memory of memories) {
        await mockMemoryDb.updateMemoryAccess(memory.id);
      }

      // Convert to context format
      const context: MemoryItem[] = memories.map(memory => ({
        id: memory.id,
        content: memory.content,
        memory_type: memory.memory_type,
        metadata: memory.metadata,
        similarity: memory.similarity || 0,
        importance_score: memory.importance_score,
        created_at: memory.created_at,
        last_accessed: memory.last_accessed
      }));

      // Cache the result
      await mockCacheManager.redis.set(cacheKey, context, { ex: this.cacheTtl });

      logger.info("Memory context retrieved", {
        query: query.substring(0, 50),
        results_count: context.length
      });

      return context;

    } catch (error) {
      logger.error("Failed to retrieve memory context", { error: String(error), query: query.substring(0, 100) });
      return [];
    }
  }

  /**
   * Search memories with high similarity threshold
   */
  async searchMemories(
    query: string,
    limit: number = 10,
    memoryType?: string,
    similarityThreshold: number = 0.7
  ): Promise<MemoryItem[]> {
    logger.info("Searching memories", { query: query.substring(0, 100), memory_type: memoryType });

    try {
      // Generate query embedding
      const queryEmbedding = await this.embeddingService.generateEmbedding(query);

      // Search memories
      const memories = await mockMemoryDb.searchMemories(
        queryEmbedding,
        limit,
        memoryType,
        similarityThreshold
      );

      // Format results
      const results: MemoryItem[] = memories
        .filter(memory => (memory.similarity || 0) >= similarityThreshold)
        .map(memory => ({
          id: memory.id,
          content: memory.content,
          memory_type: memory.memory_type,
          metadata: memory.metadata,
          similarity: memory.similarity || 0,
          importance_score: memory.importance_score,
          last_accessed: memory.last_accessed,
          created_at: memory.created_at
        }));

      logger.info("Memory search completed", {
        query: query.substring(0, 50),
        results_count: results.length
      });

      return results;

    } catch (error) {
      logger.error("Memory search failed", { error: String(error), query: query.substring(0, 100) });
      return [];
    }
  }

  /**
   * Consolidate memories to improve retrieval performance
   * Implements memory optimization for Factor 3
   */
  async consolidateMemories(executionId?: string, batchSize: number = 100): Promise<number> {
    logger.info("Starting memory consolidation", { execution_id: executionId, batch_size: batchSize });

    try {
      // Consolidate memories in database
      const consolidatedCount = await mockMemoryDb.consolidateMemories(batchSize);

      // Clear related caches to force refresh
      await mockCacheManager.invalidatePattern("memory:search:*");

      logger.info("Memory consolidation completed", {
        consolidated_count: consolidatedCount,
        execution_id: executionId
      });

      return consolidatedCount;

    } catch (error) {
      logger.error("Memory consolidation failed", {
        error: String(error),
        execution_id: executionId
      });
      return 0;
    }
  }

  /**
   * Store conversation as searchable memory
   */
  async storeConversationMemory(
    userId: string,
    conversationData: ConversationData,
    summary?: string
  ): Promise<string> {
    logger.info("Storing conversation memory", { user_id: userId });

    try {
      // Generate summary if not provided
      if (!summary) {
        summary = this.generateConversationSummary(conversationData);
      }

      // Store as memory
      const memoryId = await this.storeMemory(
        summary,
        "conversation",
        {
          user_id: userId,
          conversation_data: conversationData,
          message_count: conversationData.messages.length,
          conversation_type: "rfq_workflow"
        },
        0.6
      );

      // Also store in conversations table (mock implementation)
      const conversationEmbedding = await this.embeddingService.generateEmbedding(summary);

      // Mock conversation storage
      const conversationId = `conv_${Date.now()}`;

      logger.info("Conversation memory stored", {
        memory_id: memoryId,
        conversation_id: conversationId
      });

      return memoryId;

    } catch (error) {
      logger.error("Failed to store conversation memory", {
        error: String(error),
        user_id: userId
      });
      throw error;
    }
  }

  /**
   * Get user's conversation history
   */
  async getUserConversationHistory(userId: string, limit: number = 10): Promise<ConversationMemory[]> {
    logger.info("Retrieving conversation history", { user_id: userId, limit });

    try {
      // Mock implementation - replace with actual database query
      const conversations: ConversationMemory[] = [];

      logger.info("Conversation history retrieved", {
        user_id: userId,
        count: conversations.length
      });

      return conversations;

    } catch (error) {
      logger.error("Failed to retrieve conversation history", {
        error: String(error),
        user_id: userId
      });
      return [];
    }
  }

  /**
   * Store workflow execution as memory for future reference
   */
  async storeWorkflowMemory(
    workflowData: BaseMetadata,
    executionId: string,
    userId: string
  ): Promise<string> {
    logger.info("Storing workflow memory", { execution_id: executionId });

    try {
      // Generate workflow summary
      const summary = this.generateWorkflowSummary(workflowData);

      // Store workflow memory
      const memoryId = await this.storeMemory(
        summary,
        "workflow_execution",
        {
          execution_id: executionId,
          user_id: userId,
          workflow_type: "rfq",
          success: workflowData.success || false,
          vendor_count: (workflowData.vendors as any[])?.length || 0,
          department: workflowData.parsed_request?.department,
          urgency: workflowData.parsed_request?.urgency
        },
        workflowData.success ? 0.7 : 0.4
      );

      logger.info("Workflow memory stored", { memory_id: memoryId });
      return memoryId;

    } catch (error) {
      logger.error("Failed to store workflow memory", {
        error: String(error),
        execution_id: executionId
      });
      throw error;
    }
  }

  /**
   * Generate summary of conversation data
   */
  private generateConversationSummary(conversationData: ConversationData): string {
    const messages = conversationData.messages || [];
    if (messages.length === 0) {
      return "Empty conversation";
    }

    // Extract key information
    const userMessages = messages.filter(msg => msg.role === "user");
    const systemMessages = messages.filter(msg => msg.role === "system");

    if (userMessages.length > 0) {
      const firstUserMessage = userMessages[0].content || "";
      let summary = `User conversation started with: ${firstUserMessage.substring(0, 200)}`;

      if (userMessages.length > 1) {
        summary += ` Total user messages: ${userMessages.length}`;
      }
      return summary;
    } else {
      return `System conversation with ${systemMessages.length} messages`;
    }
  }

  /**
   * Generate summary of workflow execution
   */
  private generateWorkflowSummary(workflowData: BaseMetadata): string {
    try {
      const request = workflowData.parsed_request as any || {};
      const vendors = workflowData.vendors as any[] || [];
      const success = workflowData.success || false;

      let summary = `RFQ workflow for ${request.department || 'Unknown'} department. `;
      summary += `Request: ${(request.natural_language_request || '').substring(0, 200)}. `;
      summary += `Found ${vendors.length} vendors. `;
      summary += `Status: ${success ? 'Completed successfully' : 'Failed or incomplete'}.`;

      if (request.urgency) {
        summary += ` Urgency: ${request.urgency}.`;
      }

      if (request.budget_range) {
        summary += ` Budget: ${request.budget_range}.`;
      }

      return summary;

    } catch (error) {
      logger.error("Failed to generate workflow summary", { error: String(error) });
      return "Workflow execution summary unavailable";
    }
  }

  /**
   * Get memory system statistics
   */
  async getMemoryStatistics(): Promise<MemoryStatistics> {
    try {
      // Mock implementation - replace with actual database queries
      const statistics: MemoryStatistics = {
        total_memories: 0,
        recent_memories_24h: 0,
        memory_clusters: 0,
        memory_types: [],
        timestamp: new Date().toISOString()
      };

      return statistics;

    } catch (error) {
      logger.error("Failed to get memory statistics", { error: String(error) });
      return {
        total_memories: 0,
        recent_memories_24h: 0,
        memory_clusters: 0,
        memory_types: [],
        error: String(error),
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Hash string to number for cache keys
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Get service information
   */
  getServiceInfo(): { version: string; cache_ttl: number } {
    return {
      version: "1.0.0",
      cache_ttl: this.cacheTtl
    };
  }
}
