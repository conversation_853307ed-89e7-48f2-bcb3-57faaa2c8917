/**
 * TypeScript schemas for RFQ workflow data validation and serialization
 * Implements structured data models for Factor 4: Tools Are Just Structured Outputs
 * Converted from Python Pydantic models to TypeScript interfaces and classes
 */

import { IsString, IsNumber, IsBoolean, IsOptional, IsArray, IsEnum, IsObject, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, validate<PERSON>r<PERSON>ej<PERSON><PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';

// ========== ENUMS ==========

/**
 * Enumeration for urgency levels
 */
export enum UrgencyLevel {
  LOW = "low",
  STANDARD = "standard",
  HIGH = "high",
  CRITICAL = "critical"
}

/**
 * Enumeration for workflow stages
 */
export enum WorkflowStage {
  DISCOVERY = "discovery",
  GENERATION = "generation",
  DISTRIBUTION = "distribution",
  EVALUATION = "evaluation",
  AWARD = "award"
}

/**
 * Enumeration for execution states
 */
export enum ExecutionState {
  PENDING = "pending",
  RUNNING = "running",
  PAUSED = "paused",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled"
}

// ========== BASE INTERFACES ==========

export interface BaseMetadata {
  [key: string]: any;
}

export interface TimestampedEntity {
  created_at: string;
  updated_at?: string;
}

// ========== INPUT SCHEMAS ==========

/**
 * Input schema for RFQ processing requests
 */
export class RFQRequest {
  @IsString()
  @MinLength(10, { message: 'Natural language request must be at least 10 characters' })
  @MaxLength(5000, { message: 'Natural language request cannot exceed 5000 characters' })
  @Transform(({ value }) => value?.trim())
  natural_language_request!: string;

  @IsString()
  user_id!: string;

  @IsOptional()
  @IsString()
  request_id?: string;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsEnum(UrgencyLevel)
  urgency?: UrgencyLevel = UrgencyLevel.STANDARD;

  @IsOptional()
  @IsString()
  budget_range?: string;

  @IsOptional()
  @IsString()
  preferred_region?: string = "India";

  @IsOptional()
  @IsObject()
  additional_context?: BaseMetadata = {};

  /**
   * Validation method to ensure request is not empty
   */
  static async validate(request: RFQRequest): Promise<void> {
    if (!request.natural_language_request?.trim()) {
      throw new Error('Request cannot be empty');
    }
    await validateOrReject(request);
  }

  /**
   * Example data for documentation
   */
  static getExample(): RFQRequest {
    const example = new RFQRequest();
    example.natural_language_request = "I need 50 business laptops for our IT department. Budget around $50,000. Need them urgently for new employee onboarding in Bangalore.";
    example.user_id = "user_123";
    example.department = "IT";
    example.urgency = UrgencyLevel.HIGH;
    example.budget_range = "$50,000";
    example.preferred_region = "Bangalore";
    return example;
  }
}

// ========== CORE DATA MODELS ==========

/**
 * Vendor information model
 */
export class VendorInfo {
  @IsString()
  id!: string;

  @IsString()
  name!: string;

  @IsString()
  email!: string;

  @IsNumber()
  @Min(0)
  @Max(5)
  rating!: number;

  @IsArray()
  @IsString({ each: true })
  specialties: string[] = [];

  @IsString()
  location!: string;

  @IsOptional()
  @IsObject()
  contact_info?: BaseMetadata = {};

  @IsOptional()
  @IsObject()
  performance_history?: BaseMetadata = {};

  /**
   * Example data for documentation
   */
  static getExample(): VendorInfo {
    const example = new VendorInfo();
    example.id = "vendor_12345";
    example.name = "Premium IT Solutions Pvt Ltd";
    example.email = "<EMAIL>";
    example.rating = 4.8;
    example.specialties = ["Business Laptops", "IT Equipment"];
    example.location = "Bangalore";
    return example;
  }
}

/**
 * Quote information model
 */
export class Quote {
  @IsString()
  vendor_id!: string;

  @IsNumber()
  @Min(0)
  price!: number;

  @IsString()
  delivery_time!: string;

  @IsString()
  terms!: string;

  @IsOptional()
  @IsBoolean()
  specifications_met?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  additional_services?: string[] = [];

  @IsOptional()
  @IsString()
  validity_period?: string;
}

/**
 * Market intelligence data model
 */
export class MarketIntelligence {
  @IsString()
  price_range!: string;

  @IsString()
  market_trends!: string;

  @IsArray()
  @IsString({ each: true })
  supplier_recommendations: string[] = [];

  @IsArray()
  @IsString({ each: true })
  risk_factors: string[] = [];

  @IsArray()
  @IsString({ each: true })
  compliance_requirements: string[] = [];

  @IsOptional()
  @IsString()
  research_timestamp?: string;

  @IsOptional()
  @IsString()
  perplexity_insights?: string;

  /**
   * Example data for documentation
   */
  static getExample(): MarketIntelligence {
    const example = new MarketIntelligence();
    example.price_range = "$800-$1200 per business laptop";
    example.market_trends = "Stable pricing with 8-12% annual growth";
    example.supplier_recommendations = ["Dell Technologies", "HP Enterprise"];
    example.risk_factors = ["Supply chain delays", "Price volatility"];
    example.compliance_requirements = ["ISO 9001:2015", "Regional ESG standards"];
    return example;
  }
}

/**
 * RFQ document model
 */
export class RFQDocument {
  @IsString()
  title!: string;

  @IsString()
  description!: string;

  @IsArray()
  @IsString({ each: true })
  specifications!: string[];

  @IsNumber()
  @Min(1)
  quantity!: number;

  @IsString()
  delivery_requirements!: string;

  @IsArray()
  @IsString({ each: true })
  evaluation_criteria!: string[];

  @IsString()
  submission_deadline!: string;

  @IsOptional()
  @IsString()
  terms_and_conditions?: string;

  /**
   * Validate deadline format
   */
  static validateDeadline(deadline: string): boolean {
    try {
      // Try ISO format first
      new Date(deadline.replace('Z', '+00:00'));
      return true;
    } catch {
      try {
        // Try YYYY-MM-DD format
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(deadline)) {
          new Date(deadline);
          return true;
        }
        return false;
      } catch {
        return false;
      }
    }
  }
}

/**
 * Communication results model
 */
export class CommunicationResults {
  @IsNumber()
  @Min(0)
  total_sent!: number;

  @IsNumber()
  @Min(0)
  successful_deliveries!: number;

  @IsNumber()
  @Min(0)
  failed_deliveries!: number;

  @IsArray()
  @IsObject({ each: true })
  delivery_results: BaseMetadata[] = [];

  @IsString()
  rfq_title!: string;

  @IsString()
  submission_deadline!: string;
}

// ========== STATE MODELS ==========

/**
 * RFQ workflow state model
 */
export class RFQState implements TimestampedEntity {
  @IsString()
  rfq_id!: string;

  @IsEnum(WorkflowStage)
  current_stage!: WorkflowStage;

  @IsString()
  workflow_step!: string;

  @IsNumber()
  @Min(0)
  @Max(100)
  progress_percentage!: number;

  @IsArray()
  @Type(() => VendorInfo)
  vendors: VendorInfo[] = [];

  @IsArray()
  @Type(() => Quote)
  quotes_received: Quote[] = [];

  @IsOptional()
  @Type(() => MarketIntelligence)
  market_intelligence?: MarketIntelligence;

  @IsBoolean()
  human_approval_pending: boolean = false;

  @IsString()
  created_at!: string;

  @IsString()
  updated_at!: string;

  @IsOptional()
  @IsObject()
  error_info?: BaseMetadata;
}

/**
 * Workflow execution model
 */
export class WorkflowExecution implements TimestampedEntity {
  @IsString()
  id!: string;

  @IsString()
  workflow_id!: string;

  @IsString()
  user_id!: string;

  @IsObject()
  input_data!: BaseMetadata;

  @IsObject()
  current_state!: BaseMetadata;

  @IsEnum(ExecutionState)
  execution_state!: ExecutionState;

  @IsNumber()
  @Min(0)
  @Max(100)
  progress_percentage: number = 0;

  @IsOptional()
  @IsObject()
  error_info?: BaseMetadata;

  @IsString()
  started_at!: string;

  @IsOptional()
  @IsString()
  completed_at?: string;

  @IsString()
  created_at!: string;

  @IsOptional()
  @IsString()
  updated_at?: string;
}

/**
 * Human task model for approval workflows
 */
export class HumanTask implements TimestampedEntity {
  @IsString()
  id!: string;

  @IsString()
  execution_id!: string;

  @IsString()
  task_type!: string;

  @IsString()
  title!: string;

  @IsString()
  description!: string;

  @IsObject()
  context_data!: BaseMetadata;

  @IsOptional()
  @IsString()
  assigned_to?: string;

  @IsNumber()
  @Min(1)
  @Max(5)
  priority: number = 3;

  @IsString()
  status: string = "pending";

  @IsOptional()
  @IsObject()
  response_data?: BaseMetadata;

  @IsOptional()
  @IsString()
  deadline?: string;

  @IsString()
  created_at!: string;

  @IsOptional()
  @IsString()
  completed_at?: string;

  @IsOptional()
  @IsString()
  updated_at?: string;
}

// ========== RESPONSE SCHEMAS ==========

/**
 * System information model
 */
export class SystemInfo {
  @IsString()
  workflow_id!: string;

  @IsString()
  processing_time!: string;

  @IsString()
  api_used!: string;

  @IsBoolean()
  fallback_used: boolean = false;

  @IsOptional()
  @IsNumber()
  tool_calls_count?: number;
}

/**
 * Vendors contacted information
 */
export class VendorsContacted {
  @IsArray()
  @Type(() => VendorInfo)
  vendors!: VendorInfo[];

  @IsNumber()
  total_vendors_found!: number;

  @IsOptional()
  @IsString()
  perplexity_vendor_insights?: string;
}

/**
 * Complete RFQ workflow response
 */
export class RFQResponse {
  @IsBoolean()
  success!: boolean;

  @IsString()
  execution_id!: string;

  @Type(() => RFQState)
  rfq_state!: RFQState;

  @IsString()
  workflow_summary!: string;

  @Type(() => MarketIntelligence)
  market_intelligence!: MarketIntelligence;

  @Type(() => VendorsContacted)
  vendors_contacted!: VendorsContacted;

  @Type(() => RFQDocument)
  rfq_document!: RFQDocument;

  @Type(() => CommunicationResults)
  communication_results!: CommunicationResults;

  @IsArray()
  @IsString({ each: true })
  next_actions!: string[];

  @Type(() => SystemInfo)
  system_info!: SystemInfo;

  /**
   * Example data for documentation
   */
  static getExample(): Partial<RFQResponse> {
    return {
      success: true,
      execution_id: "exec_12345",
      workflow_summary: "RFQ processed successfully for IT department...",
      next_actions: [
        "Monitor vendor responses",
        "Follow up after 3 days"
      ]
    };
  }
}

/**
 * Error response model
 */
export class ErrorResponse {
  @IsBoolean()
  success: boolean = false;

  @IsString()
  execution_id!: string;

  @IsString()
  error!: string;

  @IsString()
  current_step!: string;

  @IsNumber()
  progress_percentage!: number;

  @IsOptional()
  @IsObject()
  error_info?: BaseMetadata;

  @IsBoolean()
  fallback_available: boolean = true;

  @IsString()
  timestamp: string = new Date().toISOString();
}

// ========== TOOL SCHEMAS ==========

/**
 * Tool call record
 */
export class ToolCall {
  @IsString()
  tool_name!: string;

  @IsObject()
  input_data!: BaseMetadata;

  @IsObject()
  output_data!: BaseMetadata;

  @IsNumber()
  execution_time!: number;

  @IsString()
  timestamp!: string;

  @IsBoolean()
  success!: boolean;

  @IsOptional()
  @IsString()
  error_info?: string;
}

/**
 * Tool definition model
 */
export class ToolDefinition {
  @IsString()
  name!: string;

  @IsString()
  description!: string;

  @IsObject()
  schema!: BaseMetadata;

  @IsString()
  implementation!: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsString()
  version: string = "1.0.0";

  @IsBoolean()
  is_active: boolean = true;
}

// ========== MEMORY SCHEMAS ==========

/**
 * Memory item model
 */
export class MemoryItem implements TimestampedEntity {
  @IsString()
  id!: string;

  @IsString()
  content!: string;

  @IsString()
  memory_type!: string;

  @IsObject()
  metadata: BaseMetadata = {};

  @IsNumber()
  @Min(0)
  @Max(1)
  importance_score!: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  similarity?: number;

  @IsNumber()
  access_count: number = 0;

  @IsOptional()
  @IsString()
  last_accessed?: string;

  @IsString()
  created_at!: string;

  @IsOptional()
  @IsString()
  updated_at?: string;
}

/**
 * Memory search request
 */
export class MemorySearchRequest {
  @IsString()
  @MinLength(1)
  query!: string;

  @IsNumber()
  @Min(1)
  @Max(50)
  limit: number = 10;

  @IsOptional()
  @IsString()
  memory_type?: string;

  @IsNumber()
  @Min(0)
  @Max(1)
  similarity_threshold: number = 0.7;
}

/**
 * Memory search response
 */
export class MemorySearchResponse {
  @IsArray()
  @Type(() => MemoryItem)
  results!: MemoryItem[];

  @IsNumber()
  total_found!: number;

  @IsString()
  query!: string;

  @IsNumber()
  search_time!: number;
}

// ========== WORKFLOW CONTROL SCHEMAS ==========

/**
 * Workflow control request
 */
export class WorkflowControlRequest {
  @IsString()
  execution_id!: string;

  @IsEnum(['pause', 'resume', 'cancel'])
  action!: 'pause' | 'resume' | 'cancel';

  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * Workflow status response
 */
export class WorkflowStatusResponse {
  @IsString()
  execution_id!: string;

  @IsEnum(ExecutionState)
  execution_state!: ExecutionState;

  @IsString()
  current_step!: string;

  @IsNumber()
  progress_percentage!: number;

  @IsString()
  started_at!: string;

  @IsOptional()
  @IsString()
  completed_at?: string;

  @IsOptional()
  @IsObject()
  error_info?: BaseMetadata;
}

/**
 * Human task response
 */
export class HumanTaskResponse {
  @IsString()
  task_id!: string;

  @IsBoolean()
  approved!: boolean;

  @IsOptional()
  @IsString()
  comments?: string;

  @IsOptional()
  @IsObject()
  modifications?: BaseMetadata;

  @IsBoolean()
  escalate: boolean = false;
}

// ========== VALIDATION UTILITIES ==========

/**
 * Validation utility class for common validation patterns
 */
export class ValidationUtils {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate date string format
   */
  static isValidDateString(dateString: string): boolean {
    try {
      const date = new Date(dateString);
      return !isNaN(date.getTime());
    } catch {
      return false;
    }
  }

  /**
   * Validate UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, '');
  }
}

// ========== TYPE GUARDS ==========

/**
 * Type guard functions for runtime type checking
 */
export class TypeGuards {
  static isRFQRequest(obj: any): obj is RFQRequest {
    return obj &&
           typeof obj.natural_language_request === 'string' &&
           typeof obj.user_id === 'string';
  }

  static isVendorInfo(obj: any): obj is VendorInfo {
    return obj &&
           typeof obj.id === 'string' &&
           typeof obj.name === 'string' &&
           typeof obj.email === 'string' &&
           typeof obj.rating === 'number';
  }

  static isRFQState(obj: any): obj is RFQState {
    return obj &&
           typeof obj.rfq_id === 'string' &&
           Object.values(WorkflowStage).includes(obj.current_stage) &&
           typeof obj.workflow_step === 'string';
  }
}

// ========== EXPORT ALL TYPES ==========

export * from './rfq';

/**
 * Union types for flexible usage
 */
export type AnyRFQModel = RFQRequest | RFQResponse | RFQState | VendorInfo | Quote | MarketIntelligence;
export type AnyWorkflowModel = WorkflowExecution | HumanTask | WorkflowControlRequest | WorkflowStatusResponse;
export type AnyToolModel = ToolCall | ToolDefinition;
export type AnyMemoryModel = MemoryItem | MemorySearchRequest | MemorySearchResponse;