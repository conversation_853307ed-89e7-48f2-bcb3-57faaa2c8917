/**
 * Services module - TypeScript version
 * Main entry point for all AI Agent services
 */

// Export all service classes
export { EmbeddingService } from './embeddingService';
export { ErrorManager } from './errorManager';
export { FunctionalWorkflowService } from './functionalWorkflowService';
export { MemoryService } from './memoryService';
export { MonitoringService } from './monitoringService';
export { PerplexityService } from './perplexityService';
export { PromptService } from './promptService';
export { StatelessAgentService } from './statelessReducer';
export { ToolService } from './toolService';
export { UniversalInputHandler } from './universalInputHandler';

// Export all types
export * from './types';

// Export service instances (if needed)
import { EmbeddingService } from './embeddingService';
import { ErrorManager } from './errorManager';
import { FunctionalWorkflowService } from './functionalWorkflowService';
import { MemoryService } from './memoryService';
import { MonitoringService } from './monitoringService';
import { PerplexityService } from './perplexityService';
import { PromptService } from './promptService';
import { StatelessAgentService } from './statelessReducer';
import { ToolService } from './toolService';
import { UniversalInputHandler } from './universalInputHandler';

// Service factory functions
export const createEmbeddingService = (dimension?: number) => new EmbeddingService(dimension);
export const createErrorManager = () => new ErrorManager();
export const createFunctionalWorkflowService = () => new FunctionalWorkflowService();
export const createMemoryService = () => new MemoryService();
export const createMonitoringService = () => new MonitoringService();
export const createPerplexityService = () => new PerplexityService();
export const createPromptService = () => new PromptService();
export const createStatelessAgentService = () => new StatelessAgentService();
export const createToolService = () => new ToolService();
export const createUniversalInputHandler = () => new UniversalInputHandler();

// Default service instances (singleton pattern)
export const embeddingService = new EmbeddingService();
export const errorManager = new ErrorManager();
export const functionalWorkflowService = new FunctionalWorkflowService();
export const memoryService = new MemoryService();
export const monitoringService = new MonitoringService();
export const perplexityService = new PerplexityService();
export const promptService = new PromptService();
export const statelessAgentService = new StatelessAgentService();
export const toolService = new ToolService();
export const universalInputHandler = new UniversalInputHandler();
