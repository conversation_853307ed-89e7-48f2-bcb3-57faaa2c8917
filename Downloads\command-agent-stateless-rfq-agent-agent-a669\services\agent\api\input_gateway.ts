/**
 * Factor 11: Input Gateway Service
 * Exposes multiple endpoints for universal input handling
 * Converted from Python FastAPI to TypeScript Express/Fastify
 */

import { Request, Response, NextFunction, Express } from 'express';
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { Logger } from 'winston';
import multer from 'multer';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

// ========== TYPE DEFINITIONS ==========

export interface TextInputRequest {
  text: string;
  source?: string;
  priority?: string;
  sender_info?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface JsonInputRequest {
  data: Record<string, any>;
  source?: string;
  priority?: string;
  sender_info?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface EmailInputRequest {
  subject: string;
  body: string;
  sender_email: string;
  priority?: string;
  attachments?: string[];
}

export interface ChatInputRequest {
  message: string;
  user_id: string;
  channel_id?: string;
  thread_id?: string;
}

export interface InputContext {
  source: InputSource;
  format: InputFormat;
  timestamp: Date;
  sender_info?: Record<string, any>;
  metadata?: Record<string, any>;
  priority: string;
}

export enum InputSource {
  API = 'api',
  EMAIL = 'email',
  CHAT = 'chat',
  FILE_UPLOAD = 'file_upload',
  WEBHOOK = 'webhook',
  SLACK = 'slack',
  TEAMS = 'teams',
  SMS = 'sms',
  VOICE = 'voice',
  WEB_FORM = 'web_form'
}

export enum InputFormat {
  TEXT = 'text',
  JSON = 'json',
  XML = 'xml',
  EMAIL_MSG = 'email_msg',
  PDF = 'pdf',
  CSV = 'csv',
  EXCEL = 'excel',
  VOICE_AUDIO = 'voice_audio',
  IMAGE = 'image',
  UNKNOWN = 'unknown'
}

export interface ProcessingResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  workflow_id?: string;
  status?: string;
}

// ========== UNIVERSAL INPUT HANDLER INTERFACE ==========

export interface UniversalInputHandler {
  processInput(rawInput: any, context: InputContext): Promise<ProcessingResult>;
  getHandlerCapabilities(): Record<string, any>;
}

// Mock implementation for now
class MockUniversalInputHandler implements UniversalInputHandler {
  async processInput(rawInput: any, context: InputContext): Promise<ProcessingResult> {
    logger.info('Processing input', {
      source: context.source,
      format: context.format,
      priority: context.priority
    });

    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 100));

    return {
      success: true,
      message: 'Input processed successfully',
      data: {
        processed_input: typeof rawInput === 'string' ? rawInput : JSON.stringify(rawInput),
        context: context,
        timestamp: new Date().toISOString()
      },
      workflow_id: `workflow_${Date.now()}`,
      status: 'processing'
    };
  }

  getHandlerCapabilities(): Record<string, any> {
    return {
      handler_id: 'universal_input_handler_ts',
      handler_type: 'universal_input',
      supported_sources: Object.values(InputSource),
      supported_formats: Object.values(InputFormat),
      supported_intents: ['rfq_creation', 'status_check', 'vendor_inquiry'],
      capabilities: [
        'multi_source_input',
        'multi_format_parsing',
        'intent_classification',
        'entity_extraction',
        'workflow_routing',
        'response_formatting'
      ],
      description: 'Universal input handler supporting any source and format',
      version: '1.0.0',
      factor_compliance: ['Factor 11: Trigger from Anywhere']
    };
  }
}

// ========== EXPRESS ROUTE HANDLERS ==========

export class InputGatewayRoutes {
  private inputHandler: UniversalInputHandler;
  private upload: multer.Multer;

  constructor(inputHandler?: UniversalInputHandler) {
    this.inputHandler = inputHandler || new MockUniversalInputHandler();

    // Configure multer for file uploads
    this.upload = multer({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
      }
    });
  }

  /**
   * Handle plain text input from any source
   */
  handleTextInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const request: TextInputRequest = req.body;

      const context: InputContext = {
        source: InputSource[request.source?.toUpperCase() as keyof typeof InputSource] || InputSource.API,
        format: InputFormat.TEXT,
        timestamp: new Date(),
        sender_info: request.sender_info,
        metadata: request.metadata,
        priority: request.priority || 'medium'
      };

      const result = await this.inputHandler.processInput(request.text, context);
      res.json(result);
    } catch (error) {
      logger.error('Text input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle JSON input from any source
   */
  handleJsonInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const request: JsonInputRequest = req.body;

      const context: InputContext = {
        source: InputSource[request.source?.toUpperCase() as keyof typeof InputSource] || InputSource.API,
        format: InputFormat.JSON,
        timestamp: new Date(),
        sender_info: request.sender_info,
        metadata: request.metadata,
        priority: request.priority || 'medium'
      };

      const result = await this.inputHandler.processInput(request.data, context);
      res.json(result);
    } catch (error) {
      logger.error('JSON input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle email input
   */
  handleEmailInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const request: EmailInputRequest = req.body;

      const emailData = {
        subject: request.subject,
        body: request.body,
        sender: request.sender_email,
        attachments: request.attachments || []
      };

      const context: InputContext = {
        source: InputSource.EMAIL,
        format: InputFormat.EMAIL_MSG,
        timestamp: new Date(),
        sender_info: { email: request.sender_email },
        priority: request.priority || 'medium'
      };

      const result = await this.inputHandler.processInput(emailData, context);
      res.json(result);
    } catch (error) {
      logger.error('Email input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle chat/messaging input
   */
  handleChatInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const request: ChatInputRequest = req.body;

      const context: InputContext = {
        source: InputSource.CHAT,
        format: InputFormat.TEXT,
        timestamp: new Date(),
        sender_info: {
          user_id: request.user_id,
          channel_id: request.channel_id,
          thread_id: request.thread_id
        },
        priority: 'medium'
      };

      const result = await this.inputHandler.processInput(request.message, context);
      res.json(result);
    } catch (error) {
      logger.error('Chat input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle file upload (PDF, CSV, Excel, etc.)
   */
  handleFileUpload = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const source = req.body.source || 'file_upload';
      const priority = req.body.priority || 'medium';
      const metadata = req.body.metadata ? JSON.parse(req.body.metadata) : {};

      // Determine format from file extension
      let fileFormat = InputFormat.UNKNOWN;
      if (file.originalname) {
        if (file.originalname.endsWith('.pdf')) {
          fileFormat = InputFormat.PDF;
        } else if (file.originalname.endsWith('.csv')) {
          fileFormat = InputFormat.CSV;
        } else if (file.originalname.endsWith('.xls') || file.originalname.endsWith('.xlsx')) {
          fileFormat = InputFormat.EXCEL;
        } else if (file.originalname.endsWith('.xml')) {
          fileFormat = InputFormat.XML;
        } else if (file.originalname.endsWith('.json')) {
          fileFormat = InputFormat.JSON;
        } else {
          fileFormat = InputFormat.TEXT;
        }
      }

      const context: InputContext = {
        source: InputSource.FILE_UPLOAD,
        format: fileFormat,
        timestamp: new Date(),
        metadata: {
          filename: file.originalname,
          content_type: file.mimetype,
          file_size: file.size,
          ...metadata
        },
        priority
      };

      const result = await this.inputHandler.processInput(file.buffer, context);
      res.json(result);
    } catch (error) {
      logger.error('File upload processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle webhook input
   */
  handleWebhookInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const inputData = req.body;
      const headers = req.headers;
      const contentType = headers['content-type'] || 'application/json';

      // Determine format from content type
      let fileFormat = InputFormat.JSON;
      if (contentType.includes('xml')) {
        fileFormat = InputFormat.XML;
      } else if (contentType.includes('text')) {
        fileFormat = InputFormat.TEXT;
      }

      const context: InputContext = {
        source: InputSource.WEBHOOK,
        format: fileFormat,
        timestamp: new Date(),
        metadata: {
          headers: headers,
          content_type: contentType,
          user_agent: headers['user-agent'],
          source_ip: req.ip
        },
        priority: 'medium'
      };

      const result = await this.inputHandler.processInput(inputData, context);
      res.json(result);
    } catch (error) {
      logger.error('Webhook input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle Slack input
   */
  handleSlackInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const slackData = req.body;

      // Extract message from Slack webhook
      let messageText = '';
      if (slackData.text) {
        messageText = slackData.text;
      } else if (slackData.event && slackData.event.text) {
        messageText = slackData.event.text;
      }

      const context: InputContext = {
        source: InputSource.SLACK,
        format: InputFormat.JSON,
        timestamp: new Date(),
        sender_info: slackData.user || slackData.event?.user || {},
        metadata: slackData,
        priority: 'medium'
      };

      const result = await this.inputHandler.processInput(messageText, context);
      res.json(result);
    } catch (error) {
      logger.error('Slack input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle Microsoft Teams input
   */
  handleTeamsInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const teamsData = req.body;

      // Extract message from Teams webhook
      let messageText = '';
      if (teamsData.text) {
        messageText = teamsData.text;
      } else if (teamsData.value && teamsData.value.text) {
        messageText = teamsData.value.text;
      }

      const context: InputContext = {
        source: InputSource.TEAMS,
        format: InputFormat.JSON,
        timestamp: new Date(),
        sender_info: teamsData.from || {},
        metadata: teamsData,
        priority: 'medium'
      };

      const result = await this.inputHandler.processInput(messageText, context);
      res.json(result);
    } catch (error) {
      logger.error('Teams input processing failed', { error });
      res.status(500).json({ error: String(error) });
    }
  };

  /**
   * Handle SMS input (Twilio format)
   */
  handleSmsInput = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { Body: message, From: fromNumber, To: toNumber, MessageSid: messageSid } = req.body;

      const context: InputContext = {
        source: InputSource.SMS,
        format: InputFormat.TEXT,
        timestamp: new Date(),
        sender_info: {
          from_number: fromNumber,
          to_number: toNumber,
          message_sid: messageSid
        },
        priority: 'high' // SMS typically urgent
      };

      const result = await this.inputHandler.processInput(message, context);

      // Return SMS-friendly response
      if (result.success) {
        res.json({
          message: 'Request processed successfully. You will receive updates via email.'
        });
      } else {
        res.json({
          message: `Processing failed: ${result.error || 'Unknown error'}`
        });
      }
    } catch (error) {
      logger.error('SMS input processing failed', { error });
      res.json({
        message: `Error: ${String(error)}`
      });
    }
  };

  /**
   * Get list of supported input sources and formats
   */
  getSupportedSources = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const capabilities = this.inputHandler.getHandlerCapabilities();

      res.json({
        supported_sources: Object.values(InputSource),
        supported_formats: Object.values(InputFormat),
        endpoints: [
          '/api/input/text',
          '/api/input/json',
          '/api/input/email',
          '/api/input/chat',
          '/api/input/file',
          '/api/input/webhook',
          '/api/input/slack',
          '/api/input/teams',
          '/api/input/sms'
        ],
        capabilities
      });
    } catch (error) {
      logger.error('Failed to get supported sources', { error });
      res.status(500).json({ error: String(error) });
    }
  };
}

// ========== EXPRESS ROUTE SETUP ==========

/**
 * Create input gateway routes for Express app
 */
export function createInputGatewayRoutes(app: Express, inputHandler?: UniversalInputHandler): void {
  const routes = new InputGatewayRoutes(inputHandler);

  // Configure multer middleware for file uploads
  const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: 10 * 1024 * 1024 // 10MB limit
    }
  });

  // Register routes
  app.post('/api/input/text', routes.handleTextInput);
  app.post('/api/input/json', routes.handleJsonInput);
  app.post('/api/input/email', routes.handleEmailInput);
  app.post('/api/input/chat', routes.handleChatInput);
  app.post('/api/input/file', upload.single('file'), routes.handleFileUpload);
  app.post('/api/input/webhook', routes.handleWebhookInput);
  app.post('/api/input/slack', routes.handleSlackInput);
  app.post('/api/input/teams', routes.handleTeamsInput);
  app.post('/api/input/sms', routes.handleSmsInput);
  app.get('/api/input/sources', routes.getSupportedSources);

  logger.info('Input gateway routes registered successfully');
}

// ========== FASTIFY PLUGIN ==========

/**
 * Fastify plugin for input gateway routes
 */
export const inputGatewayPlugin = {
  name: 'input-gateway',
  register: async (fastify: FastifyInstance, options: { inputHandler?: UniversalInputHandler }) => {
    const routes = new InputGatewayRoutes(options.inputHandler);

    // Register multipart support for file uploads
    await fastify.register(require('@fastify/multipart'));

    // Text input endpoint
    fastify.post('/api/input/text', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const body = request.body as TextInputRequest;

        const context: InputContext = {
          source: InputSource[body.source?.toUpperCase() as keyof typeof InputSource] || InputSource.API,
          format: InputFormat.TEXT,
          timestamp: new Date(),
          sender_info: body.sender_info,
          metadata: body.metadata,
          priority: body.priority || 'medium'
        };

        const result = await routes['inputHandler'].processInput(body.text, context);
        reply.send(result);
      } catch (error) {
        logger.error('Text input processing failed', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    // JSON input endpoint
    fastify.post('/api/input/json', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const body = request.body as JsonInputRequest;

        const context: InputContext = {
          source: InputSource[body.source?.toUpperCase() as keyof typeof InputSource] || InputSource.API,
          format: InputFormat.JSON,
          timestamp: new Date(),
          sender_info: body.sender_info,
          metadata: body.metadata,
          priority: body.priority || 'medium'
        };

        const result = await routes['inputHandler'].processInput(body.data, context);
        reply.send(result);
      } catch (error) {
        logger.error('JSON input processing failed', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    // Email input endpoint
    fastify.post('/api/input/email', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const body = request.body as EmailInputRequest;

        const emailData = {
          subject: body.subject,
          body: body.body,
          sender: body.sender_email,
          attachments: body.attachments || []
        };

        const context: InputContext = {
          source: InputSource.EMAIL,
          format: InputFormat.EMAIL_MSG,
          timestamp: new Date(),
          sender_info: { email: body.sender_email },
          priority: body.priority || 'medium'
        };

        const result = await routes['inputHandler'].processInput(emailData, context);
        reply.send(result);
      } catch (error) {
        logger.error('Email input processing failed', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    // Chat input endpoint
    fastify.post('/api/input/chat', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const body = request.body as ChatInputRequest;

        const context: InputContext = {
          source: InputSource.CHAT,
          format: InputFormat.TEXT,
          timestamp: new Date(),
          sender_info: {
            user_id: body.user_id,
            channel_id: body.channel_id,
            thread_id: body.thread_id
          },
          priority: 'medium'
        };

        const result = await routes['inputHandler'].processInput(body.message, context);
        reply.send(result);
      } catch (error) {
        logger.error('Chat input processing failed', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    // File upload endpoint
    fastify.post('/api/input/file', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const data = await request.file();
        if (!data) {
          return reply.status(400).send({ error: 'No file uploaded' });
        }

        const fileBuffer = await data.toBuffer();
        const filename = data.filename;
        const mimetype = data.mimetype;

        // Determine format from file extension
        let fileFormat = InputFormat.UNKNOWN;
        if (filename) {
          if (filename.endsWith('.pdf')) {
            fileFormat = InputFormat.PDF;
          } else if (filename.endsWith('.csv')) {
            fileFormat = InputFormat.CSV;
          } else if (filename.endsWith('.xls') || filename.endsWith('.xlsx')) {
            fileFormat = InputFormat.EXCEL;
          } else if (filename.endsWith('.xml')) {
            fileFormat = InputFormat.XML;
          } else if (filename.endsWith('.json')) {
            fileFormat = InputFormat.JSON;
          } else {
            fileFormat = InputFormat.TEXT;
          }
        }

        const context: InputContext = {
          source: InputSource.FILE_UPLOAD,
          format: fileFormat,
          timestamp: new Date(),
          metadata: {
            filename,
            content_type: mimetype,
            file_size: fileBuffer.length
          },
          priority: 'medium'
        };

        const result = await routes['inputHandler'].processInput(fileBuffer, context);
        reply.send(result);
      } catch (error) {
        logger.error('File upload processing failed', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    // Webhook endpoint
    fastify.post('/api/input/webhook', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const inputData = request.body;
        const headers = request.headers;
        const contentType = headers['content-type'] || 'application/json';

        let fileFormat = InputFormat.JSON;
        if (contentType.includes('xml')) {
          fileFormat = InputFormat.XML;
        } else if (contentType.includes('text')) {
          fileFormat = InputFormat.TEXT;
        }

        const context: InputContext = {
          source: InputSource.WEBHOOK,
          format: fileFormat,
          timestamp: new Date(),
          metadata: {
            headers,
            content_type: contentType,
            user_agent: headers['user-agent'],
            source_ip: request.ip
          },
          priority: 'medium'
        };

        const result = await routes['inputHandler'].processInput(inputData, context);
        reply.send(result);
      } catch (error) {
        logger.error('Webhook input processing failed', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    // Get supported sources endpoint
    fastify.get('/api/input/sources', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const capabilities = routes['inputHandler'].getHandlerCapabilities();

        reply.send({
          supported_sources: Object.values(InputSource),
          supported_formats: Object.values(InputFormat),
          endpoints: [
            '/api/input/text',
            '/api/input/json',
            '/api/input/email',
            '/api/input/chat',
            '/api/input/file',
            '/api/input/webhook',
            '/api/input/slack',
            '/api/input/teams',
            '/api/input/sms'
          ],
          capabilities
        });
      } catch (error) {
        logger.error('Failed to get supported sources', { error });
        reply.status(500).send({ error: String(error) });
      }
    });

    logger.info('Input gateway Fastify plugin registered successfully');
  }
};

// ========== UTILITY FUNCTIONS ==========

/**
 * Create input context from request data
 */
export function createInputContext(
  source: InputSource,
  format: InputFormat,
  priority: string = 'medium',
  senderInfo?: Record<string, any>,
  metadata?: Record<string, any>
): InputContext {
  return {
    source,
    format,
    timestamp: new Date(),
    sender_info: senderInfo,
    metadata,
    priority
  };
}

/**
 * Validate input request
 */
export function validateInputRequest(request: any, requiredFields: string[]): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  for (const field of requiredFields) {
    if (!request[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Format error response
 */
export function formatErrorResponse(error: any, context?: string): ProcessingResult {
  return {
    success: false,
    error: String(error),
    message: context ? `${context}: ${String(error)}` : String(error),
    status: 'error'
  };
}

// Export default
export default {
  InputGatewayRoutes,
  createInputGatewayRoutes,
  inputGatewayPlugin,
  MockUniversalInputHandler,
  InputSource,
  InputFormat
};