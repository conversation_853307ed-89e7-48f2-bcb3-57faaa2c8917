/**
 * Universal Input Handler implementing Factor 11: Trigger from Anywhere - TypeScript version
 * Handles multi-source, multi-format input processing with intent classification
 */

import {
  InputRequest,
  ProcessedInput,
  IntentClassification,
  InputValidationResult,
  InputHandlerCapabilities,
  BaseMetadata,
  Logger
} from './types';
import { EmbeddingService } from './embeddingService';
import { PerplexityService } from './perplexityService';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

/**
 * Universal input processing service implementing Factor 11
 * Handles inputs from multiple sources and formats with intelligent routing
 */
export class UniversalInputHandler {
  private readonly embeddingService: EmbeddingService;
  private readonly perplexityService: PerplexityService;
  private readonly supportedFormats: Set<string>;
  private readonly supportedSources: Set<string>;

  constructor() {
    this.embeddingService = new EmbeddingService();
    this.perplexityService = new PerplexityService();
    
    this.supportedFormats = new Set([
      "text", "json", "xml", "csv", "email", "form_data", "multipart", "url_encoded"
    ]);
    
    this.supportedSources = new Set([
      "web_form", "api", "email", "chat", "file_upload", "webhook", "cli", "mobile_app"
    ]);
  }

  /**
   * Process input from any source and format
   * Core implementation of Factor 11: Trigger from Anywhere
   */
  async processInput(inputRequest: InputRequest): Promise<ProcessedInput> {
    const startTime = new Date();
    const requestId = inputRequest.request_id || `req_${Date.now()}`;

    logger.info("Processing universal input", {
      request_id: requestId,
      source: inputRequest.source,
      format: inputRequest.format,
      content_length: inputRequest.content?.length || 0
    });

    try {
      // Step 1: Validate input
      const validation = await this.validateInput(inputRequest);
      if (!validation.is_valid) {
        throw new Error(`Input validation failed: ${validation.errors?.join(', ')}`);
      }

      // Step 2: Parse content based on format
      const parsedContent = await this.parseContent(inputRequest.content, inputRequest.format);

      // Step 3: Extract structured data
      const extractedData = await this.extractStructuredData(parsedContent, inputRequest.format);

      // Step 4: Classify intent
      const intentClassification = await this.classifyIntent(extractedData, inputRequest);

      // Step 5: Enrich with context
      const enrichedData = await this.enrichWithContext(extractedData, intentClassification);

      // Step 6: Route to appropriate workflow
      const routingDecision = await this.determineRouting(intentClassification, enrichedData);

      const processingTime = (new Date().getTime() - startTime.getTime()) / 1000;

      const processedInput: ProcessedInput = {
        request_id: requestId,
        original_request: inputRequest,
        parsed_content: parsedContent,
        extracted_data: extractedData,
        intent_classification: intentClassification,
        enriched_data: enrichedData,
        routing_decision: routingDecision,
        processing_time: processingTime,
        timestamp: startTime.toISOString(),
        success: true
      };

      logger.info("Input processed successfully", {
        request_id: requestId,
        intent: intentClassification.primary_intent,
        confidence: intentClassification.confidence,
        processing_time: processingTime
      });

      return processedInput;

    } catch (error) {
      const processingTime = (new Date().getTime() - startTime.getTime()) / 1000;

      logger.error("Input processing failed", {
        request_id: requestId,
        error: String(error),
        processing_time: processingTime
      });

      return {
        request_id: requestId,
        original_request: inputRequest,
        parsed_content: {},
        extracted_data: {},
        intent_classification: {
          primary_intent: "error",
          confidence: 0.0,
          secondary_intents: [],
          classification_metadata: {}
        },
        enriched_data: {},
        routing_decision: {
          workflow_name: "error_handling",
          parameters: { error: String(error) }
        },
        processing_time: processingTime,
        timestamp: startTime.toISOString(),
        success: false,
        error: String(error)
      };
    }
  }

  /**
   * Validate input request
   */
  private async validateInput(inputRequest: InputRequest): Promise<InputValidationResult> {
    const errors: string[] = [];

    // Check required fields
    if (!inputRequest.content) {
      errors.push("Content is required");
    }

    if (!inputRequest.source) {
      errors.push("Source is required");
    }

    if (!inputRequest.format) {
      errors.push("Format is required");
    }

    // Check supported formats and sources
    if (inputRequest.format && !this.supportedFormats.has(inputRequest.format)) {
      errors.push(`Unsupported format: ${inputRequest.format}`);
    }

    if (inputRequest.source && !this.supportedSources.has(inputRequest.source)) {
      errors.push(`Unsupported source: ${inputRequest.source}`);
    }

    // Content length validation
    if (inputRequest.content && inputRequest.content.length > 100000) {
      errors.push("Content too large (max 100KB)");
    }

    return {
      is_valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  /**
   * Parse content based on format
   */
  private async parseContent(content: string, format: string): Promise<BaseMetadata> {
    try {
      switch (format) {
        case "json":
          return JSON.parse(content);

        case "xml":
          return this.parseXml(content);

        case "csv":
          return this.parseCsv(content);

        case "email":
          return this.parseEmail(content);

        case "form_data":
          return this.parseFormData(content);

        case "url_encoded":
          return this.parseUrlEncoded(content);

        case "text":
        default:
          return { raw_text: content };
      }
    } catch (error) {
      logger.error("Content parsing failed", { format, error: String(error) });
      return { raw_text: content, parse_error: String(error) };
    }
  }

  /**
   * Extract structured data from parsed content
   */
  private async extractStructuredData(
    parsedContent: BaseMetadata,
    format: string
  ): Promise<BaseMetadata> {
    const extractedData: BaseMetadata = { ...parsedContent };

    // Extract common fields
    if (parsedContent.raw_text) {
      const text = parsedContent.raw_text as string;
      
      // Extract email addresses
      const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
      const emails = text.match(emailRegex);
      if (emails) {
        extractedData.extracted_emails = emails;
      }

      // Extract phone numbers (simple pattern)
      const phoneRegex = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g;
      const phones = text.match(phoneRegex);
      if (phones) {
        extractedData.extracted_phones = phones;
      }

      // Extract URLs
      const urlRegex = /https?:\/\/[^\s]+/g;
      const urls = text.match(urlRegex);
      if (urls) {
        extractedData.extracted_urls = urls;
      }

      // Extract monetary amounts
      const moneyRegex = /\$[\d,]+(?:\.\d{2})?/g;
      const amounts = text.match(moneyRegex);
      if (amounts) {
        extractedData.extracted_amounts = amounts;
      }
    }

    // Format-specific extraction
    if (format === "email") {
      extractedData.email_subject = parsedContent.subject;
      extractedData.email_sender = parsedContent.from;
      extractedData.email_recipients = parsedContent.to;
    }

    return extractedData;
  }

  /**
   * Classify intent using AI and pattern matching
   */
  private async classifyIntent(
    extractedData: BaseMetadata,
    inputRequest: InputRequest
  ): Promise<IntentClassification> {
    try {
      // Prepare text for classification
      let classificationText = "";
      
      if (extractedData.raw_text) {
        classificationText = extractedData.raw_text as string;
      } else {
        classificationText = JSON.stringify(extractedData).substring(0, 1000);
      }

      // Generate embedding for intent classification
      const embedding = await this.embeddingService.generateEmbedding(classificationText);

      // Rule-based classification (fast path)
      const ruleBasedIntent = this.classifyIntentByRules(classificationText, extractedData);
      if (ruleBasedIntent.confidence > 0.8) {
        return ruleBasedIntent;
      }

      // AI-powered classification for complex cases
      const aiIntent = await this.classifyIntentWithAI(classificationText);

      // Combine rule-based and AI classification
      const finalIntent: IntentClassification = {
        primary_intent: aiIntent.primary_intent || ruleBasedIntent.primary_intent,
        confidence: Math.max(aiIntent.confidence || 0, ruleBasedIntent.confidence),
        secondary_intents: [
          ...(aiIntent.secondary_intents || []),
          ...(ruleBasedIntent.secondary_intents || [])
        ].slice(0, 3),
        classification_metadata: {
          rule_based_intent: ruleBasedIntent.primary_intent,
          ai_intent: aiIntent.primary_intent,
          source: inputRequest.source,
          format: inputRequest.format,
          embedding_used: true
        }
      };

      return finalIntent;

    } catch (error) {
      logger.error("Intent classification failed", { error: String(error) });
      
      return {
        primary_intent: "unknown",
        confidence: 0.0,
        secondary_intents: [],
        classification_metadata: { error: String(error) }
      };
    }
  }

  /**
   * Rule-based intent classification
   */
  private classifyIntentByRules(text: string, extractedData: BaseMetadata): IntentClassification {
    const lowerText = text.toLowerCase();

    // RFQ/Procurement intent patterns
    if (lowerText.includes("rfq") || lowerText.includes("request for quote") || 
        lowerText.includes("procurement") || lowerText.includes("purchase")) {
      return {
        primary_intent: "rfq_request",
        confidence: 0.9,
        secondary_intents: ["procurement"],
        classification_metadata: { rule: "rfq_keywords" }
      };
    }

    // Vendor discovery intent
    if (lowerText.includes("vendor") || lowerText.includes("supplier") || 
        lowerText.includes("find") && (lowerText.includes("company") || lowerText.includes("provider"))) {
      return {
        primary_intent: "vendor_discovery",
        confidence: 0.85,
        secondary_intents: ["research"],
        classification_metadata: { rule: "vendor_keywords" }
      };
    }

    // Market research intent
    if (lowerText.includes("market") || lowerText.includes("research") || 
        lowerText.includes("analysis") || lowerText.includes("trends")) {
      return {
        primary_intent: "market_research",
        confidence: 0.8,
        secondary_intents: ["analysis"],
        classification_metadata: { rule: "research_keywords" }
      };
    }

    // Question/inquiry intent
    if (lowerText.includes("?") || lowerText.startsWith("what") || 
        lowerText.startsWith("how") || lowerText.startsWith("when")) {
      return {
        primary_intent: "inquiry",
        confidence: 0.7,
        secondary_intents: ["question"],
        classification_metadata: { rule: "question_patterns" }
      };
    }

    // Default classification
    return {
      primary_intent: "general",
      confidence: 0.5,
      secondary_intents: [],
      classification_metadata: { rule: "default" }
    };
  }

  /**
   * AI-powered intent classification using Perplexity
   */
  private async classifyIntentWithAI(text: string): Promise<Partial<IntentClassification>> {
    try {
      const classificationPrompt = `
      Classify the intent of this user input for a procurement AI agent system.
      
      Input: "${text.substring(0, 500)}"
      
      Possible intents:
      - rfq_request: User wants to create an RFQ or procurement request
      - vendor_discovery: User wants to find suppliers or vendors
      - market_research: User wants market analysis or research
      - inquiry: User has a question or needs information
      - document_generation: User wants to generate documents
      - general: General conversation or unclear intent
      
      Respond with just the intent name and confidence (0.0-1.0).
      `;

      const result = await this.perplexityService.generateSummary({ query: classificationPrompt });
      
      // Simple parsing of AI response
      const lines = result.split('\n');
      let intent = "general";
      let confidence = 0.5;

      for (const line of lines) {
        const trimmedLine = line.trim().toLowerCase();
        if (trimmedLine.includes("intent:") || trimmedLine.includes("classification:")) {
          const intentMatch = trimmedLine.match(/(rfq_request|vendor_discovery|market_research|inquiry|document_generation|general)/);
          if (intentMatch) {
            intent = intentMatch[1];
          }
        }
        if (trimmedLine.includes("confidence:")) {
          const confidenceMatch = trimmedLine.match(/(\d+\.?\d*)/);
          if (confidenceMatch) {
            confidence = Math.min(1.0, Math.max(0.0, parseFloat(confidenceMatch[1])));
          }
        }
      }

      return {
        primary_intent: intent,
        confidence,
        secondary_intents: [],
        classification_metadata: { ai_classification: true }
      };

    } catch (error) {
      logger.error("AI intent classification failed", { error: String(error) });
      return {
        primary_intent: "general",
        confidence: 0.3,
        classification_metadata: { ai_error: String(error) }
      };
    }
  }

  /**
   * Enrich data with additional context
   */
  private async enrichWithContext(
    extractedData: BaseMetadata,
    intentClassification: IntentClassification
  ): Promise<BaseMetadata> {
    const enrichedData = { ...extractedData };

    // Add intent-based enrichment
    enrichedData.classified_intent = intentClassification.primary_intent;
    enrichedData.intent_confidence = intentClassification.confidence;

    // Add timestamp and processing metadata
    enrichedData.processed_at = new Date().toISOString();
    enrichedData.enrichment_version = "1.0.0";

    // Intent-specific enrichment
    if (intentClassification.primary_intent === "rfq_request") {
      enrichedData.workflow_type = "rfq_processing";
      enrichedData.priority = this.determinePriority(extractedData);
    } else if (intentClassification.primary_intent === "vendor_discovery") {
      enrichedData.workflow_type = "vendor_research";
      enrichedData.research_scope = "vendor_identification";
    } else if (intentClassification.primary_intent === "market_research") {
      enrichedData.workflow_type = "market_analysis";
      enrichedData.research_scope = "market_intelligence";
    }

    return enrichedData;
  }

  /**
   * Determine routing decision based on intent and data
   */
  private async determineRouting(
    intentClassification: IntentClassification,
    enrichedData: BaseMetadata
  ): Promise<BaseMetadata> {
    const routing: BaseMetadata = {
      workflow_name: "default_processing",
      parameters: {}
    };

    switch (intentClassification.primary_intent) {
      case "rfq_request":
        routing.workflow_name = "rfq_workflow";
        routing.parameters = {
          input_data: enrichedData,
          priority: enrichedData.priority || "normal"
        };
        break;

      case "vendor_discovery":
        routing.workflow_name = "vendor_discovery_workflow";
        routing.parameters = {
          search_query: enrichedData.raw_text,
          research_scope: "vendor_identification"
        };
        break;

      case "market_research":
        routing.workflow_name = "market_research_workflow";
        routing.parameters = {
          research_query: enrichedData.raw_text,
          research_type: "market_analysis"
        };
        break;

      case "inquiry":
        routing.workflow_name = "inquiry_response_workflow";
        routing.parameters = {
          question: enrichedData.raw_text,
          context: enrichedData
        };
        break;

      default:
        routing.workflow_name = "general_processing_workflow";
        routing.parameters = {
          input_data: enrichedData,
          intent: intentClassification.primary_intent
        };
    }

    return routing;
  }

  // ========== PARSING UTILITIES ==========

  private parseXml(content: string): BaseMetadata {
    // Simple XML parsing - would use proper XML parser in production
    const result: BaseMetadata = { raw_xml: content };
    
    // Extract basic XML structure
    const tagRegex = /<(\w+)>([^<]*)<\/\1>/g;
    let match;
    while ((match = tagRegex.exec(content)) !== null) {
      result[match[1]] = match[2];
    }
    
    return result;
  }

  private parseCsv(content: string): BaseMetadata {
    const lines = content.split('\n');
    if (lines.length < 2) {
      return { raw_csv: content };
    }

    const headers = lines[0].split(',').map(h => h.trim());
    const rows = lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim());
      const row: BaseMetadata = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    return { headers, rows, row_count: rows.length };
  }

  private parseEmail(content: string): BaseMetadata {
    const result: BaseMetadata = { raw_email: content };
    
    // Extract email headers (simple parsing)
    const lines = content.split('\n');
    let bodyStart = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (line.trim() === '') {
        bodyStart = i + 1;
        break;
      }
      
      if (line.includes(':')) {
        const [key, ...valueParts] = line.split(':');
        const value = valueParts.join(':').trim();
        result[key.toLowerCase().trim()] = value;
      }
    }
    
    result.body = lines.slice(bodyStart).join('\n');
    return result;
  }

  private parseFormData(content: string): BaseMetadata {
    const result: BaseMetadata = {};
    const pairs = content.split('&');
    
    for (const pair of pairs) {
      const [key, value] = pair.split('=');
      if (key && value) {
        result[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    }
    
    return result;
  }

  private parseUrlEncoded(content: string): BaseMetadata {
    return this.parseFormData(content); // Same parsing logic
  }

  private determinePriority(data: BaseMetadata): string {
    const text = (data.raw_text as string || '').toLowerCase();
    
    if (text.includes('urgent') || text.includes('asap') || text.includes('emergency')) {
      return 'high';
    } else if (text.includes('soon') || text.includes('priority')) {
      return 'medium';
    }
    
    return 'normal';
  }

  /**
   * Get service capabilities
   */
  getInputHandlerCapabilities(): InputHandlerCapabilities {
    return {
      service_id: "universal_input_handler",
      supported_sources: Array.from(this.supportedSources),
      supported_formats: Array.from(this.supportedFormats),
      intent_classification: true,
      ai_powered_classification: true,
      context_enrichment: true,
      automatic_routing: true,
      version: "1.0.0",
      factor_compliance: ["Factor 11: Trigger from Anywhere"]
    };
  }

  /**
   * Get service information
   */
  getServiceInfo(): { version: string; supported_formats: number; supported_sources: number } {
    return {
      version: "1.0.0",
      supported_formats: this.supportedFormats.size,
      supported_sources: this.supportedSources.size
    };
  }
}
