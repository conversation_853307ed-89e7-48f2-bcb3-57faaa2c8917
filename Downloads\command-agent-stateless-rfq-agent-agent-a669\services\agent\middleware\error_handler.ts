/**
 * Error handling middleware
 * Converted from Python Starlette to TypeScript Express/Fastify middleware
 */

import { Request, Response, NextFunction } from 'express';
import { Logger } from 'winston';

// Create logger (mock implementation for now)
const logger: Logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${message}`, meta),
} as Logger;

/**
 * Error response interface
 */
export interface ErrorResponse {
  error: string;
  detail?: string;
  timestamp?: string;
  path?: string;
  status?: number;
}

/**
 * Custom error class for application errors
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * HTTP error classes
 */
export class BadRequestError extends AppError {
  constructor(message: string = 'Bad Request') {
    super(message, 400);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Not Found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Conflict') {
    super(message, 409);
  }
}

export class ValidationError extends AppError {
  constructor(message: string = 'Validation Error') {
    super(message, 422);
  }
}

export class InternalServerError extends AppError {
  constructor(message: string = 'Internal Server Error') {
    super(message, 500);
  }
}

/**
 * Error handler middleware for Express
 */
export class ErrorHandlerMiddleware {
  /**
   * Express error handling middleware
   */
  static handle() {
    return (error: Error, req: Request, res: Response, next: NextFunction): void => {
      logger.error('Unhandled error', {
        error: error.message,
        path: req.path,
        method: req.method,
        stack: error.stack
      });

      // Default error response
      let statusCode = 500;
      let message = 'Internal server error';
      let detail: string | undefined;

      // Handle known application errors
      if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
        detail = error.isOperational ? error.message : undefined;
      }

      // Handle validation errors
      if (error.name === 'ValidationError') {
        statusCode = 422;
        message = 'Validation Error';
        detail = error.message;
      }

      // Handle JSON parsing errors
      if (error instanceof SyntaxError && 'body' in error) {
        statusCode = 400;
        message = 'Invalid JSON';
        detail = 'Request body contains invalid JSON';
      }

      const errorResponse: ErrorResponse = {
        error: message,
        timestamp: new Date().toISOString(),
        path: req.path,
        status: statusCode
      };

      // Include detail in development mode or for operational errors
      if (detail || process.env.NODE_ENV === 'development') {
        errorResponse.detail = detail || error.message;
      }

      res.status(statusCode).json(errorResponse);
    };
  }

  /**
   * Async error wrapper for route handlers
   */
  static asyncHandler(fn: Function) {
    return (req: Request, res: Response, next: NextFunction) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * 404 handler middleware
   */
  static notFound() {
    return (req: Request, res: Response, next: NextFunction): void => {
      const error = new NotFoundError(`Route ${req.originalUrl} not found`);
      next(error);
    };
  }
}

/**
 * Fastify error handler plugin
 */
export const fastifyErrorHandler = {
  name: 'error-handler',
  register: async (fastify: any) => {
    fastify.setErrorHandler((error: Error, request: any, reply: any) => {
      logger.error('Unhandled error', {
        error: error.message,
        path: request.url,
        method: request.method,
        stack: error.stack
      });

      let statusCode = 500;
      let message = 'Internal server error';
      let detail: string | undefined;

      if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
        detail = error.isOperational ? error.message : undefined;
      }

      const errorResponse: ErrorResponse = {
        error: message,
        timestamp: new Date().toISOString(),
        path: request.url,
        status: statusCode
      };

      if (detail || process.env.NODE_ENV === 'development') {
        errorResponse.detail = detail || error.message;
      }

      reply.status(statusCode).send(errorResponse);
    });

    fastify.setNotFoundHandler((request: any, reply: any) => {
      const errorResponse: ErrorResponse = {
        error: 'Not Found',
        detail: `Route ${request.url} not found`,
        timestamp: new Date().toISOString(),
        path: request.url,
        status: 404
      };

      reply.status(404).send(errorResponse);
    });
  }
};

/**
 * Global error handler utilities
 */
export class GlobalErrorHandler {
  /**
   * Handle uncaught exceptions
   */
  static handleUncaughtException(): void {
    process.on('uncaughtException', (error: Error) => {
      logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack
      });

      // Graceful shutdown
      process.exit(1);
    });
  }

  /**
   * Handle unhandled promise rejections
   */
  static handleUnhandledRejection(): void {
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      logger.error('Unhandled Rejection', {
        reason: reason instanceof Error ? reason.message : reason,
        stack: reason instanceof Error ? reason.stack : undefined
      });

      // Graceful shutdown
      process.exit(1);
    });
  }

  /**
   * Setup all global error handlers
   */
  static setup(): void {
    this.handleUncaughtException();
    this.handleUnhandledRejection();
  }
}

// Export default error handler
export default ErrorHandlerMiddleware;