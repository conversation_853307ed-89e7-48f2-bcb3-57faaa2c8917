# Environment Configuration for AI Agent System
# Copy this to .env and fill in the actual values

# ========== DATABASE CONFIGURATION ==========
# Supabase Configuration
SUPABASE_URL=https://lazbyqleijuzefmhiznm.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxhemJ5cWxlaWp1emVmbWhpem5tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg2MjA0MTgsImV4cCI6MjA3NDE5NjQxOH0.H4hNNk1qJ_zgPO8PgIA-Gb-C2tb7aKONaIwaudEMTXQ

# Legacy PostgreSQL settings (deprecated - using Supabase instead)
# POSTGRES_PASSWORD=552156307@Khalidiyah
# POSTGRES_DB=agent_db
# POSTGRES_USER=agent_user

# ========== REDIS CONFIGURATION ==========
REDIS_PASSWORD=your_secure_redis_password_here

# ========== MESSAGE QUEUE CONFIGURATION ==========
RABBITMQ_USER=agent_user
RABBITMQ_PASSWORD=your_secure_rabbitmq_password_here

# ========== API KEYS ==========
PERPLEXITY_API_KEY=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

# ========== MONITORING ==========
GRAFANA_PASSWORD=your_secure_grafana_password_here

# ========== BUILD CONFIGURATION ==========
BUILD_DATE=2025-01-01T00:00:00Z
GIT_COMMIT=main

# ========== ENVIRONMENT ==========
ENVIRONMENT=production
LOG_LEVEL=INFO

# ========== SECURITY ==========
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# ========== FEATURE FLAGS ==========
ENABLE_HUMAN_IN_LOOP=true
ENABLE_ADVANCED_MEMORY=true
ENABLE_MONITORING=true
ENABLE_ASYNC_PROCESSING=true

# ========== PERFORMANCE TUNING ==========
MAX_CONCURRENT_WORKFLOWS=50
CONTEXT_WINDOW_SIZE=32000
EMBEDDING_BATCH_SIZE=32
MEMORY_CONSOLIDATION_INTERVAL=3600

# ========== NETWORKING ==========
EXTERNAL_HOST=localhost
API_PORT=8000
NGINX_PORT=80
NGINX_SSL_PORT=443