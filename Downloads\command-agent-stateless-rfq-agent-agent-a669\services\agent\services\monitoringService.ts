/**
 * Comprehensive Monitoring Service - TypeScript version
 * Implements metrics collection for all 12-Factor Agent components
 */

import {
  SystemHealth,
  MetricsSummary,
  MonitoringCapabilities,
  MetricLabels,
  BaseMetadata,
  Logger
} from './types';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

// Mock Prometheus-like metrics interfaces
interface Counter {
  labels(labels: MetricLabels): { inc(): void };
}

interface Histogram {
  labels(labels: MetricLabels): { observe(value: number): void };
}

interface Gauge {
  labels?(labels: MetricLabels): { set(value: number): void };
  set(value: number): void;
  inc(): void;
  dec(): void;
}

// Mock metrics implementations
const createCounter = (name: string, help: string, labelNames?: string[]): Counter => ({
  labels: (labels: MetricLabels) => ({
    inc: () => console.debug(`Counter ${name} incremented`, labels)
  })
});

const createHistogram = (name: string, help: string, labelNames?: string[]): Histogram => ({
  labels: (labels: MetricLabels) => ({
    observe: (value: number) => console.debug(`Histogram ${name} observed ${value}`, labels)
  })
});

const createGauge = (name: string, help: string, labelNames?: string[]): Gauge => ({
  labels: labelNames ? (labels: MetricLabels) => ({
    set: (value: number) => console.debug(`Gauge ${name} set to ${value}`, labels)
  }) : undefined,
  set: (value: number) => console.debug(`Gauge ${name} set to ${value}`),
  inc: () => console.debug(`Gauge ${name} incremented`),
  dec: () => console.debug(`Gauge ${name} decremented`)
});

/**
 * Centralized metrics collection for the AI Agent system
 * Tracks all 12-Factor Agent components with detailed metrics
 */
class MetricsCollector {
  // ========== GENERAL SYSTEM METRICS ==========
  public readonly httpRequestsTotal: Counter;
  public readonly httpRequestDuration: Histogram;

  // ========== FACTOR 1: NATURAL LANGUAGE TO TOOL CALLS ==========
  public readonly toolCallsTotal: Counter;
  public readonly toolCallDuration: Histogram;
  public readonly perplexityApiCalls: Counter;

  // ========== FACTOR 2: OWN YOUR PROMPTS ==========
  public readonly promptRetrievals: Counter;
  public readonly promptCacheHits: Counter;

  // ========== FACTOR 3: OWN YOUR CONTEXT WINDOW ==========
  public readonly memorySearches: Counter;
  public readonly memorySearchDuration: Histogram;
  public readonly memoryConsolidations: Counter;
  public readonly contextWindowSize: Histogram;

  // ========== FACTOR 4: TOOLS ARE STRUCTURED OUTPUTS ==========
  public readonly toolRegistrations: Counter;
  public readonly structuredOutputValidations: Counter;

  // ========== FACTOR 5: UNIFY EXECUTION STATE ==========
  public readonly stateUpdates: Counter;
  public readonly databaseOperations: Counter;

  // ========== FACTOR 6: LAUNCH/PAUSE/RESUME ==========
  public readonly workflowCheckpoints: Counter;
  public readonly workflowPauses: Counter;
  public readonly workflowResumes: Counter;

  // ========== FACTOR 7: CONTACT HUMANS ==========
  public readonly humanTasksCreated: Counter;
  public readonly humanTasksCompleted: Counter;
  public readonly humanTaskDuration: Histogram;
  public readonly humanTasksPending: Gauge;

  // ========== FACTOR 8: OWN YOUR CONTROL FLOW ==========
  public readonly langgraphWorkflows: Counter;
  public readonly langgraphWorkflowDuration: Histogram;
  public readonly workflowSteps: Counter;

  // ========== FACTOR 9: COMPACT ERRORS ==========
  public readonly errorCompactions: Counter;
  public readonly errorPatternMatches: Counter;
  public readonly errorContextSize: Histogram;

  // ========== FACTOR 10: SMALL FOCUSED AGENTS ==========
  public readonly agentExecutions: Counter;
  public readonly agentCoordination: Counter;
  public readonly agentQualityScores: Histogram;

  // ========== FACTOR 11: TRIGGER FROM ANYWHERE ==========
  public readonly inputRequests: Counter;
  public readonly inputProcessingDuration: Histogram;
  public readonly intentClassifications: Counter;

  // ========== FACTOR 12: STATELESS REDUCER ==========
  public readonly stateReductions: Counter;
  public readonly functionalOperations: Counter;
  public readonly stateSerialization: Counter;

  // ========== VECTOR DATABASE METRICS ==========
  public readonly pgvectorSearches: Counter;
  public readonly pgvectorSearchDuration: Histogram;
  public readonly pgvectorIndexSize: Gauge;

  // ========== BUSINESS METRICS ==========
  public readonly rfqCompletions: Counter;
  public readonly vendorDiscoveries: Counter;
  public readonly documentGenerations: Counter;
  public readonly marketResearchOperations: Counter;

  // ========== PERFORMANCE GAUGES ==========
  public readonly activeWorkflows: Gauge;
  public readonly memoryUsageBytes: Gauge;
  public readonly queueDepth: Gauge;

  constructor() {
    this.setupMetrics();
  }

  private setupMetrics(): void {
    // Initialize all metrics
    this.httpRequestsTotal = createCounter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status']);
    this.httpRequestDuration = createHistogram('http_request_duration_seconds', 'HTTP request duration', ['method', 'endpoint']);

    this.toolCallsTotal = createCounter('tool_calls_total', 'Total tool calls made', ['tool_name', 'status']);
    this.toolCallDuration = createHistogram('tool_call_duration_seconds', 'Tool call execution time', ['tool_name']);
    this.perplexityApiCalls = createCounter('perplexity_api_calls_total', 'Total Perplexity API calls', ['operation_type', 'status']);

    this.promptRetrievals = createCounter('prompt_retrievals_total', 'Total prompt retrievals', ['prompt_name', 'version']);
    this.promptCacheHits = createCounter('prompt_cache_hits_total', 'Prompt cache hits');

    this.memorySearches = createCounter('memory_searches_total', 'Total memory searches', ['search_type']);
    this.memorySearchDuration = createHistogram('memory_search_duration_seconds', 'Memory search duration', ['search_type']);
    this.memoryConsolidations = createCounter('memory_consolidation_operations_total', 'Memory consolidation operations', ['status']);
    this.contextWindowSize = createHistogram('context_window_size_tokens', 'Context window size in tokens', ['operation']);

    this.toolRegistrations = createCounter('tool_registrations_total', 'Tool registrations', ['tool_type']);
    this.structuredOutputValidations = createCounter('structured_output_validations_total', 'Structured output validations', ['tool_name', 'status']);

    this.stateUpdates = createCounter('agent_state_updates_total', 'Agent state updates', ['state_type']);
    this.databaseOperations = createCounter('database_operations_total', 'Database operations', ['operation_type', 'table', 'status']);

    this.workflowCheckpoints = createCounter('workflow_checkpoints_total', 'Workflow checkpoints created', ['workflow_type']);
    this.workflowPauses = createCounter('workflow_pauses_total', 'Workflow pauses', ['workflow_type']);
    this.workflowResumes = createCounter('workflow_resumes_total', 'Workflow resumes', ['workflow_type']);

    this.humanTasksCreated = createCounter('human_tasks_created_total', 'Human tasks created', ['task_type']);
    this.humanTasksCompleted = createCounter('human_tasks_completed_total', 'Human tasks completed', ['task_type']);
    this.humanTaskDuration = createHistogram('human_task_duration_seconds', 'Human task completion time', ['task_type']);
    this.humanTasksPending = createGauge('human_tasks_pending_total', 'Currently pending human tasks');

    this.langgraphWorkflows = createCounter('langgraph_workflow_executions_total', 'LangGraph workflow executions', ['workflow_type', 'status']);
    this.langgraphWorkflowDuration = createHistogram('langgraph_workflow_duration_seconds', 'LangGraph workflow duration', ['workflow_type']);
    this.workflowSteps = createCounter('workflow_steps_total', 'Workflow steps executed', ['workflow_type', 'step_name', 'status']);

    this.errorCompactions = createCounter('error_manager_compactions_total', 'Error compactions performed');
    this.errorPatternMatches = createCounter('error_manager_pattern_matches_total', 'Error pattern matches found');
    this.errorContextSize = createHistogram('error_context_size_chars', 'Error context size after compaction');

    this.agentExecutions = createCounter('agent_executions_total', 'Agent executions', ['agent_type', 'status']);
    this.agentCoordination = createCounter('multi_agent_coordinations_total', 'Multi-agent coordinations', ['coordination_type']);
    this.agentQualityScores = createHistogram('agent_quality_scores', 'Agent execution quality scores', ['agent_type']);

    this.inputRequests = createCounter('input_requests_total', 'Input requests processed', ['source', 'format', 'status']);
    this.inputProcessingDuration = createHistogram('input_processing_duration_seconds', 'Input processing duration', ['source', 'format']);
    this.intentClassifications = createCounter('intent_classifications_total', 'Intent classifications', ['intent', 'confidence_level']);

    this.stateReductions = createCounter('state_reductions_total', 'State reductions performed', ['action_type']);
    this.functionalOperations = createCounter('functional_operations_total', 'Functional operations executed', ['operation_type', 'status']);
    this.stateSerialization = createCounter('state_serializations_total', 'State serializations', ['serialization_type']);

    this.pgvectorSearches = createCounter('pgvector_searches_total', 'PGVector searches performed');
    this.pgvectorSearchDuration = createHistogram('pgvector_search_duration_seconds', 'PGVector search duration');
    this.pgvectorIndexSize = createGauge('pgvector_index_size_bytes', 'PGVector index size in bytes');

    this.rfqCompletions = createCounter('rfq_completions_total', 'RFQ workflow completions', ['status']);
    this.vendorDiscoveries = createCounter('vendor_discoveries_total', 'Vendor discoveries', ['vendor_tier']);
    this.documentGenerations = createCounter('document_generations_total', 'Document generations', ['document_type', 'status']);
    this.marketResearchOperations = createCounter('market_research_operations_total', 'Market research operations', ['research_type', 'status']);

    this.activeWorkflows = createGauge('active_workflows_current', 'Currently active workflows');
    this.memoryUsageBytes = createGauge('memory_usage_bytes', 'Memory usage in bytes', ['component']);
    this.queueDepth = createGauge('queue_depth_current', 'Current queue depth', ['queue_name']);
  }

  // ========== METRIC RECORDING METHODS ==========

  recordHttpRequest(method: string, endpoint: string, status: number, duration: number): void {
    this.httpRequestsTotal.labels({ method, endpoint, status: status.toString() }).inc();
    this.httpRequestDuration.labels({ method, endpoint }).observe(duration);
  }

  recordToolCall(toolName: string, status: string, duration: number): void {
    this.toolCallsTotal.labels({ tool_name: toolName, status }).inc();
    this.toolCallDuration.labels({ tool_name: toolName }).observe(duration);
  }

  recordMemorySearch(searchType: string, duration: number): void {
    this.memorySearches.labels({ search_type: searchType }).inc();
    this.memorySearchDuration.labels({ search_type: searchType }).observe(duration);
  }

  recordWorkflowExecution(workflowType: string, status: string, duration: number): void {
    this.langgraphWorkflows.labels({ workflow_type: workflowType, status }).inc();
    this.langgraphWorkflowDuration.labels({ workflow_type: workflowType }).observe(duration);
  }

  recordAgentExecution(agentType: string, status: string, qualityScore: number): void {
    this.agentExecutions.labels({ agent_type: agentType, status }).inc();
    this.agentQualityScores.labels({ agent_type: agentType }).observe(qualityScore);
  }

  recordInputProcessing(source: string, format: string, status: string, duration: number): void {
    this.inputRequests.labels({ source, format, status }).inc();
    this.inputProcessingDuration.labels({ source, format }).observe(duration);
  }

  recordHumanTask(taskType: string, status: string, duration?: number): void {
    if (status === 'created') {
      this.humanTasksCreated.labels({ task_type: taskType }).inc();
      this.humanTasksPending.inc();
    } else if (status === 'completed') {
      this.humanTasksCompleted.labels({ task_type: taskType }).inc();
      this.humanTasksPending.dec();
      if (duration !== undefined) {
        this.humanTaskDuration.labels({ task_type: taskType }).observe(duration);
      }
    }
  }

  recordErrorCompaction(contextSize: number): void {
    this.errorCompactions.labels({}).inc();
    this.errorContextSize.labels({}).observe(contextSize);
  }

  recordStateReduction(actionType: string): void {
    this.stateReductions.labels({ action_type: actionType }).inc();
  }

  updateActiveWorkflows(count: number): void {
    this.activeWorkflows.set(count);
  }

  updateQueueDepth(queueName: string, depth: number): void {
    if (this.queueDepth.labels) {
      this.queueDepth.labels({ queue_name: queueName }).set(depth);
    }
  }

  getMetrics(): string {
    // Mock implementation - would return Prometheus format
    return "# Mock Prometheus metrics output";
  }
}

// Global metrics collector instance
const metricsCollector = new MetricsCollector();

/**
 * Decorator to monitor execution time of functions
 */
export function monitorExecutionTime(metricName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      try {
        const result = await method.apply(this, args);
        const duration = (Date.now() - startTime) / 1000;
        
        // Record metric based on metric name
        logger.debug(`Function ${propertyName} executed in ${duration}s`);
        
        return result;
      } catch (error) {
        const duration = (Date.now() - startTime) / 1000;
        logger.error(`Function ${propertyName} failed after ${duration}s: ${error}`);
        throw error;
      }
    };
  };
}

/**
 * Monitoring service for comprehensive system observability
 * Integrates with Prometheus and Grafana for visualization
 */
export class MonitoringService {
  private readonly collector: MetricsCollector;
  private readonly serviceId: string;

  constructor() {
    this.collector = metricsCollector;
    this.serviceId = "monitoring_service";
  }

  getSystemHealth(): SystemHealth {
    return {
      timestamp: new Date().toISOString(),
      service_status: "healthy",
      metrics_collected: true,
      prometheus_endpoint: "/metrics",
      grafana_dashboard: "/grafana/d/ai-agent-overview",
      alert_rules_active: true
    };
  }

  getMetricsSummary(): MetricsSummary {
    return {
      http_requests: "Total HTTP requests processed",
      tool_calls: "AI tool calls and executions",
      memory_operations: "Memory search and consolidation operations",
      workflow_executions: "LangGraph workflow executions",
      agent_coordinations: "Multi-agent orchestrations",
      input_processing: "Universal input handler operations",
      human_tasks: "Human-in-the-loop task management",
      state_management: "Stateless reducer operations",
      vector_database: "PGVector operations and performance",
      business_metrics: "RFQ workflow and business logic metrics"
    };
  }

  exportMetrics(): string {
    return this.collector.getMetrics();
  }

  getMonitoringCapabilities(): MonitoringCapabilities {
    return {
      service_id: this.serviceId,
      monitoring_type: "prometheus_grafana",
      capabilities: [
        "real_time_metrics_collection",
        "performance_monitoring",
        "error_tracking",
        "business_metrics",
        "alerting_rules",
        "dashboard_visualization"
      ],
      metrics_categories: [
        "system_health",
        "performance_metrics",
        "business_logic",
        "error_management",
        "resource_utilization"
      ],
      factor_coverage: "All 12 factors monitored",
      description: "Comprehensive monitoring for LangGraph AI Agent system",
      version: "1.0.0",
      endpoints: {
        metrics: "/metrics",
        health: "/api/monitoring/health",
        summary: "/api/monitoring/summary"
      }
    };
  }

  // Expose collector methods
  recordHttpRequest = this.collector.recordHttpRequest.bind(this.collector);
  recordToolCall = this.collector.recordToolCall.bind(this.collector);
  recordMemorySearch = this.collector.recordMemorySearch.bind(this.collector);
  recordWorkflowExecution = this.collector.recordWorkflowExecution.bind(this.collector);
  recordAgentExecution = this.collector.recordAgentExecution.bind(this.collector);
  recordInputProcessing = this.collector.recordInputProcessing.bind(this.collector);
  recordHumanTask = this.collector.recordHumanTask.bind(this.collector);
  recordErrorCompaction = this.collector.recordErrorCompaction.bind(this.collector);
  recordStateReduction = this.collector.recordStateReduction.bind(this.collector);
  updateActiveWorkflows = this.collector.updateActiveWorkflows.bind(this.collector);
  updateQueueDepth = this.collector.updateQueueDepth.bind(this.collector);

  /**
   * Get service information
   */
  getServiceInfo(): { service_id: string; version: string; metrics_count: number } {
    return {
      service_id: this.serviceId,
      version: "1.0.0",
      metrics_count: Object.keys(this.collector).length
    };
  }
}
