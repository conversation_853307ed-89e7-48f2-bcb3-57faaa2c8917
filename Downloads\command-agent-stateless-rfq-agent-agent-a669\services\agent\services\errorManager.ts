/**
 * Error Management Service implementing Factor 9: Compact Errors into Context Window - TypeScript version
 * Provides intelligent error handling, pattern recognition, and context-aware error compaction
 */

import { createHash } from 'crypto';
import {
  ErrorInfo,
  ErrorPattern,
  ErrorAnalytics,
  EmbeddingVector,
  BaseMetadata,
  Logger,
  DatabaseQueryParams,
  DatabaseResult
} from './types';
import { EmbeddingService } from './embeddingService';
import { PerplexityService } from './perplexityService';

// Mock logger implementation - replace with actual logger
const logger: Logger = {
  debug: (message: string, meta?: BaseMetadata) => console.debug(message, meta),
  info: (message: string, meta?: BaseMetadata) => console.info(message, meta),
  warn: (message: string, meta?: BaseMetadata) => console.warn(message, meta),
  error: (message: string, meta?: BaseMetadata) => console.error(message, meta),
};

// Mock database service - replace with actual database implementation
const mockDbService = {
  async executeQuery(query: string, params?: DatabaseQueryParams): Promise<DatabaseResult[]> {
    // Mock implementation
    return [];
  },
  async executeSingleQuery(query: string, params?: DatabaseQueryParams): Promise<DatabaseResult | null> {
    // Mock implementation
    return null;
  },
  async executeCommand(query: string, params?: DatabaseQueryParams): Promise<void> {
    // Mock implementation
  }
};

// Mock cache manager - replace with actual implementation
const mockCacheManager = {
  async invalidatePattern(pattern: string): Promise<void> {
    // Mock implementation
  }
};

/**
 * Comprehensive error management implementing Factor 9
 * Features: Pattern recognition, context compaction, learning from errors
 */
export class ErrorManager {
  private readonly embeddingService: EmbeddingService;
  private readonly perplexityService: PerplexityService;
  private readonly maxContextLength: number;
  private readonly patternSimilarityThreshold: number;

  constructor() {
    this.embeddingService = new EmbeddingService();
    this.perplexityService = new PerplexityService();
    this.maxContextLength = 1000; // Maximum characters for compacted error context
    this.patternSimilarityThreshold = 0.8;
  }

  /**
   * Compact error information for inclusion in context window
   * Core implementation of Factor 9
   */
  async compactErrorForContext(
    error: Error,
    context: BaseMetadata,
    executionId: string,
    stepName: string
  ): Promise<string> {
    logger.info("Compacting error for context", {
      execution_id: executionId,
      step_name: stepName,
      error_type: error.constructor.name
    });

    try {
      // Generate error signature
      const errorSignature = this.generateErrorSignature(error, stepName);

      // Check for existing error patterns
      const similarErrors = await this.findSimilarErrorPatterns(errorSignature, error);

      // Create base error info
      const errorInfo: ErrorInfo = {
        error_type: error.constructor.name,
        step: stepName,
        message: error.message.substring(0, 200), // Limit message length
        signature: errorSignature,
        timestamp: new Date().toISOString(),
        execution_id: executionId
      };

      // Add context compaction
      const compactedContext = await this.compactContext(context);
      errorInfo.context = compactedContext;

      // Add solution suggestions if patterns exist
      if (similarErrors.length > 0) {
        const solutions = await this.generateSolutionSuggestions(similarErrors);
        errorInfo.suggested_solutions = solutions;

        // Use historical success rate for confidence
        const successRate = await this.calculatePatternSuccessRate(errorSignature);
        errorInfo.solution_confidence = successRate;
      }

      // Store error pattern for learning
      await this.storeErrorPattern(errorInfo, similarErrors);

      // Generate compacted error summary
      const compactedSummary = await this.generateCompactedSummary(errorInfo);

      logger.info("Error compacted successfully", {
        execution_id: executionId,
        compacted_length: compactedSummary.length,
        has_solutions: similarErrors.length > 0
      });

      return compactedSummary;

    } catch (e) {
      logger.error("Failed to compact error", {
        execution_id: executionId,
        compaction_error: String(e)
      });

      // Fallback: simple error compaction
      return `Error in ${stepName}: ${error.message.substring(0, 100)}... (compaction failed)`;
    }
  }

  /**
   * Generate unique signature for error pattern recognition
   */
  private generateErrorSignature(error: Error, stepName: string): string {
    // Normalize error message
    let errorMessage = error.message.toLowerCase();

    // Remove variable parts (numbers, IDs, timestamps)
    errorMessage = errorMessage.replace(/\d+/g, 'N');
    errorMessage = errorMessage.replace(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g, 'UUID');
    errorMessage = errorMessage.replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/g, 'TIMESTAMP');

    // Create signature
    const signatureData = `${error.constructor.name}:${stepName}:${errorMessage}`;
    return createHash('md5').update(signatureData).digest('hex');
  }

  /**
   * Find similar error patterns using vector similarity
   */
  private async findSimilarErrorPatterns(
    errorSignature: string,
    error: Error
  ): Promise<ErrorPattern[]> {
    try {
      // Generate embedding for current error
      const errorText = `${error.constructor.name} ${error.message}`;
      const errorEmbedding = await this.embeddingService.generateEmbedding(errorText);

      // Mock database query for similar errors
      // In real implementation, would use vector similarity search
      const results: ErrorPattern[] = [];

      return results;

    } catch (e) {
      logger.error("Failed to find similar error patterns", { error: String(e) });
      return [];
    }
  }

  /**
   * Compact context information to fit within limits
   */
  private async compactContext(context: BaseMetadata): Promise<BaseMetadata> {
    const compacted: BaseMetadata = {};

    // Priority order for context elements
    const priorityKeys = [
      "execution_id", "user_id", "current_step", "progress_percentage",
      "parsed_request", "vendors", "market_intelligence"
    ];

    let remainingLength = this.maxContextLength;

    for (const key of priorityKeys) {
      if (key in context && remainingLength > 0) {
        const value = context[key];

        // Serialize and check length
        const serialized = JSON.stringify(value);

        if (serialized.length <= remainingLength) {
          compacted[key] = value;
          remainingLength -= serialized.length;
        } else {
          // Truncate or summarize
          if (typeof value === 'object' && value !== null) {
            const valueObj = value as any;
            if (Array.isArray(valueObj)) {
              compacted[key] = `<Array with ${valueObj.length} items>`;
            } else {
              compacted[key] = `<Object with ${Object.keys(valueObj).length} properties>`;
            }
          } else {
            const truncated = String(value).substring(0, remainingLength - 10);
            compacted[key] = `${truncated}...`;
          }
          break;
        }
      }
    }

    return compacted;
  }

  /**
   * Generate solution suggestions based on similar error patterns
   */
  private async generateSolutionSuggestions(similarErrors: ErrorPattern[]): Promise<string[]> {
    const solutions: string[] = [];

    for (const errorPattern of similarErrors) {
      if (errorPattern.solution_steps && errorPattern.solution_steps.length > 0) {
        solutions.push(...errorPattern.solution_steps);
      }
    }

    // Remove duplicates and limit
    const uniqueSolutions = Array.from(new Set(solutions)).slice(0, 3);

    // If no stored solutions, generate AI-powered suggestions
    if (uniqueSolutions.length === 0 && similarErrors.length > 0) {
      const aiSolutions = await this.generateAiSolutions(similarErrors);
      uniqueSolutions.push(...aiSolutions);
    }

    return uniqueSolutions;
  }

  /**
   * Generate AI-powered solution suggestions using Perplexity
   */
  private async generateAiSolutions(similarErrors: ErrorPattern[]): Promise<string[]> {
    try {
      const errorDescriptions = similarErrors.slice(0, 2).map(error =>
        `${error.error_type}: ${error.description || 'No description'}`
      );

      const query = `
      Based on these similar error patterns in an AI agent system:
      ${errorDescriptions.join('; ')}
      
      Provide 2-3 specific, actionable solution steps to resolve these types of errors.
      Focus on practical debugging and recovery steps.
      `;

      const result = await this.perplexityService.generateSummary({ query });

      // Extract solution steps (simple parsing)
      const solutions: string[] = [];
      const lines = result.split('\n');
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine && (trimmedLine.startsWith('-') || trimmedLine.startsWith('•') || /^\d+/.test(trimmedLine))) {
          solutions.push(trimmedLine.substring(0, 100)); // Limit length
        }
      }

      return solutions.slice(0, 3);

    } catch (e) {
      logger.error("Failed to generate AI solutions", { error: String(e) });
      return [];
    }
  }

  /**
   * Calculate success rate for error pattern resolution
   */
  private async calculatePatternSuccessRate(errorSignature: string): Promise<number> {
    try {
      // Mock implementation - would query actual database
      return 0.0;

    } catch (e) {
      logger.error("Failed to calculate success rate", { error: String(e) });
      return 0.0;
    }
  }

  /**
   * Store error pattern for future learning
   */
  private async storeErrorPattern(
    errorInfo: ErrorInfo,
    similarErrors: ErrorPattern[]
  ): Promise<void> {
    try {
      // Generate embedding for error
      const errorText = `${errorInfo.error_type} ${errorInfo.message}`;
      const embedding = await this.embeddingService.generateEmbedding(errorText);

      // Prepare data for storage
      const patternData: Partial<ErrorPattern> = {
        error_signature: errorInfo.signature,
        error_type: errorInfo.error_type,
        description: errorInfo.message,
        embedding,
        occurrence_count: 1,
        resolved_count: 0,
        solution_steps: errorInfo.suggested_solutions || [],
        context_pattern: errorInfo.context || {},
        step_name: errorInfo.step
      };

      // Mock database storage
      logger.info("Error pattern stored", {
        signature: errorInfo.signature,
        has_similar: similarErrors.length > 0
      });

    } catch (e) {
      logger.error("Failed to store error pattern", { error: String(e) });
    }
  }

  /**
   * Generate final compacted error summary for context window
   */
  private async generateCompactedSummary(errorInfo: ErrorInfo): Promise<string> {
    // Base error information
    const summaryParts = [
      `Error in ${errorInfo.step}: ${errorInfo.error_type}`
    ];

    // Add truncated message
    let message = errorInfo.message;
    if (message.length > 100) {
      message = message.substring(0, 97) + "...";
    }
    summaryParts.push(`Details: ${message}`);

    // Add solutions if available
    const solutions = errorInfo.suggested_solutions || [];
    if (solutions.length > 0) {
      const confidence = errorInfo.solution_confidence || 0;
      summaryParts.push(`Solutions (confidence: ${(confidence * 100).toFixed(1)}%): ${solutions.slice(0, 2).join('; ')}`);
    }

    // Add relevant context
    const context = errorInfo.context || {};
    if (context.current_step) {
      summaryParts.push(`Step: ${context.current_step}`);
    }

    if (context.progress_percentage) {
      summaryParts.push(`Progress: ${context.progress_percentage}%`);
    }

    // Join and ensure within length limits
    let fullSummary = summaryParts.join(" | ");

    if (fullSummary.length > this.maxContextLength) {
      fullSummary = fullSummary.substring(0, this.maxContextLength - 3) + "...";
    }

    return fullSummary;
  }

  /**
   * Mark an error pattern as resolved and update success metrics
   */
  async markErrorResolved(
    errorSignature: string,
    resolutionSteps: string[],
    success: boolean = true
  ): Promise<void> {
    try {
      // Mock implementation - would update actual database
      logger.info("Error resolution recorded", {
        signature: errorSignature,
        success
      });

    } catch (e) {
      logger.error("Failed to mark error resolved", { error: String(e) });
    }
  }

  /**
   * Get comprehensive error analytics for monitoring
   */
  async getErrorAnalytics(): Promise<ErrorAnalytics> {
    try {
      // Mock implementation - would query actual database
      const analytics: ErrorAnalytics = {
        error_types: [],
        recent_trends: [],
        top_unresolved: [],
        generated_at: new Date().toISOString()
      };

      return analytics;

    } catch (e) {
      logger.error("Failed to get error analytics", { error: String(e) });
      return {
        error_types: [],
        recent_trends: [],
        top_unresolved: [],
        generated_at: new Date().toISOString()
      };
    }
  }

  /**
   * Suggest error prevention strategies based on historical patterns
   */
  async suggestErrorPrevention(workflowType: string = "rfq"): Promise<string[]> {
    try {
      // Mock implementation - would analyze actual error patterns
      const preventionStrategies = [
        "Implement circuit breaker pattern for external API calls",
        "Add comprehensive health checks and monitoring alerts",
        "Use structured logging with error correlation IDs"
      ];

      return preventionStrategies.slice(0, 7); // Limit to top recommendations

    } catch (e) {
      logger.error("Failed to suggest error prevention", { error: String(e) });
      return ["Error prevention analysis unavailable"];
    }
  }

  /**
   * Get service information
   */
  getServiceInfo(): { version: string; max_context_length: number; similarity_threshold: number } {
    return {
      version: "1.0.0",
      max_context_length: this.maxContextLength,
      similarity_threshold: this.patternSimilarityThreshold
    };
  }
}

// Global error manager instance
export const errorManager = new ErrorManager();
